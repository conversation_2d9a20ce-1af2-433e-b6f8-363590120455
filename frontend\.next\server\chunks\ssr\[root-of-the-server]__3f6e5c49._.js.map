{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/i18n.ts"], "sourcesContent": ["export type Locale = 'ar' | 'fr' | 'en';\n\nexport const locales: Locale[] = ['ar', 'fr', 'en'];\n\nexport const defaultLocale: Locale = 'ar';\n\nexport const localeNames = {\n  ar: 'العربية',\n  fr: 'Français', \n  en: 'English'\n};\n\nexport const localeFlags = {\n  ar: '🇲🇦',\n  fr: '🇫🇷',\n  en: '🇬🇧'\n};\n\nexport const rtlLocales: Locale[] = ['ar'];\n\nexport function isRtlLocale(locale: Locale): boolean {\n  return rtlLocales.includes(locale);\n}\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,UAAoB;IAAC;IAAM;IAAM;CAAK;AAE5C,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,aAAuB;IAAC;CAAK;AAEnC,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTranslation.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react';\nimport { Locale, defaultLocale } from '@/lib/i18n';\n\n// Import translation files\nimport arTranslations from '@/locales/ar.json';\nimport frTranslations from '@/locales/fr.json';\nimport enTranslations from '@/locales/en.json';\n\nconst translations = {\n  ar: arTranslations,\n  fr: frTranslations,\n  en: enTranslations,\n};\n\nexport function useTranslation() {\n  const [locale, setLocale] = useState<Locale>(defaultLocale);\n\n  useEffect(() => {\n    // Get locale from localStorage or use default\n    const savedLocale = localStorage.getItem('locale') as Locale;\n    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {\n      setLocale(savedLocale);\n    }\n  }, []);\n\n  const changeLocale = (newLocale: Locale) => {\n    setLocale(newLocale);\n    localStorage.setItem('locale', newLocale);\n    \n    // Update document direction and language\n    document.documentElement.lang = newLocale;\n    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[locale];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return {\n    locale,\n    changeLocale,\n    t,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA,2BAA2B;AAC3B;AACA;AACA;AARA;;;;;;AAUA,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,kHAAA,CAAA,gBAAa;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,IAAI,eAAe;YAAC;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,cAAc;YAC3D,UAAU;QACZ;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,aAAa,OAAO,CAAC,UAAU;QAE/B,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,cAAc,OAAO,QAAQ;IAC9D;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,YAAY,CAAC,OAAO;QAErC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/NavigationSkeleton.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\n\ninterface NavigationSkeletonProps {\n  isMobile?: boolean\n}\n\nexport function NavigationSkeleton({ isMobile = false }: NavigationSkeletonProps) {\n  const skeletonItems = Array.from({ length: 5 }, (_, i) => i)\n\n  if (isMobile) {\n    return (\n      <div className=\"flex flex-col gap-1 mb-6\">\n        {skeletonItems.map((index) => (\n          <div\n            key={index}\n            className=\"flex items-center gap-3 px-4 py-3 mx-2 rounded-xl animate-pulse\"\n            style={{\n              animationDelay: `${index * 100}ms`\n            }}\n          >\n            {/* Icon skeleton */}\n            <div className=\"w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded shimmer\" />\n            \n            {/* Text skeleton */}\n            <div className=\"flex-1\">\n              <div \n                className=\"h-4 bg-gray-300 dark:bg-gray-600 rounded shimmer\"\n                style={{ width: `${60 + Math.random() * 40}%` }}\n              />\n            </div>\n          </div>\n        ))}\n      </div>\n    )\n  }\n\n  return (\n    <nav className=\"hidden lg:flex items-center gap-1\">\n      {skeletonItems.map((index) => (\n        <div\n          key={index}\n          className=\"flex items-center gap-2 px-4 py-2.5 rounded-xl animate-pulse\"\n          style={{\n            animationDelay: `${index * 100}ms`\n          }}\n        >\n          {/* Icon skeleton */}\n          <div className=\"w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded shimmer\" />\n          \n          {/* Text skeleton */}\n          <div \n            className=\"h-4 bg-gray-300 dark:bg-gray-600 rounded shimmer\"\n            style={{ width: `${50 + Math.random() * 30}px` }}\n          />\n        </div>\n      ))}\n    </nav>\n  )\n}\n\n// Skeleton للعناصر الجانبية (Cart, Wishlist, etc.)\nexport function SideElementsSkeleton() {\n  return (\n    <div className=\"flex items-center gap-3\">\n      {/* Search skeleton */}\n      <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse\" />\n      \n      {/* Cart skeleton */}\n      <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse\" style={{ animationDelay: '100ms' }} />\n      \n      {/* Wishlist skeleton */}\n      <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse\" style={{ animationDelay: '200ms' }} />\n      \n      {/* Theme toggle skeleton */}\n      <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse\" style={{ animationDelay: '300ms' }} />\n      \n      {/* Language skeleton */}\n      <div className=\"w-16 h-8 bg-gray-300 dark:bg-gray-600 rounded shimmer animate-pulse\" style={{ animationDelay: '400ms' }} />\n      \n      {/* Profile skeleton */}\n      <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full shimmer animate-pulse\" style={{ animationDelay: '500ms' }} />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAQO,SAAS,mBAAmB,EAAE,WAAW,KAAK,EAA2B;IAC9E,MAAM,gBAAgB,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAE,GAAG,CAAC,GAAG,IAAM;IAE1D,IAAI,UAAU;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC;oBAEC,WAAU;oBACV,OAAO;wBACL,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;oBACpC;;sCAGA,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;gCAAC;;;;;;;;;;;;mBAb7C;;;;;;;;;;IAoBf;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC;gBAEC,WAAU;gBACV,OAAO;oBACL,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gBACpC;;kCAGA,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,EAAE,CAAC;wBAAC;;;;;;;eAZ5C;;;;;;;;;;AAkBf;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;gBAA0E,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;0BAG1H,8OAAC;gBAAI,WAAU;gBAA0E,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;0BAG1H,8OAAC;gBAAI,WAAU;gBAA0E,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;0BAG1H,8OAAC;gBAAI,WAAU;gBAAsE,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;0BAGtH,8OAAC;gBAAI,WAAU;gBAA0E,OAAO;oBAAE,gBAAgB;gBAAQ;;;;;;;;;;;;AAGhI", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 571, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/language-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Languages, Check, Globe } from \"lucide-react\"\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Locale, localeNames, localeFlags } from \"@/lib/i18n\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function LanguageToggle() {\n  const { locale, changeLocale } = useTranslation()\n\n  const getCurrentLanguageInfo = () => {\n    return {\n      flag: localeFlags[locale],\n      name: localeNames[locale],\n      code: locale.toUpperCase()\n    }\n  }\n\n  const currentLang = getCurrentLanguageInfo()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg group-hover:scale-110 transition-transform duration-300\">\n              {currentLang.flag}\n            </span>\n            <span className=\"hidden sm:inline-block text-sm font-medium\">\n              {currentLang.code}\n            </span>\n          </div>\n          <Globe className=\"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300\" />\n          <span className=\"sr-only\">تغيير اللغة / Change language</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent\n        align=\"end\"\n        className=\"w-48 p-2\"\n        sideOffset={8}\n      >\n        <DropdownMenuLabel className=\"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1\">\n          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        {Object.entries(localeNames).map(([code, name]) => (\n          <DropdownMenuItem\n            key={code}\n            onClick={() => changeLocale(code as Locale)}\n            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${\n              locale === code\n                ? \"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300\"\n                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\n            }`}\n          >\n            <span className=\"text-lg\">{localeFlags[code as Locale]}</span>\n            <div className=\"flex-1\">\n              <div className=\"font-medium text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}\n              </div>\n            </div>\n            {locale === code && (\n              <Check className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n            )}\n          </DropdownMenuItem>\n        ))}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AAEA;AACA;AARA;;;;;;;AAiBO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE9C,MAAM,yBAAyB;QAC7B,OAAO;YACL,MAAM,kHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,kHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,8OAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;;;;;;;sCAGrB,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAEZ,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC1B,WAAW,OAAO,eAAe,WAAW,OAAO,sBAAsB;;;;;;kCAE5E,8OAAC,4IAAA,CAAA,wBAAqB;;;;;oBACrB,OAAO,OAAO,CAAC,kHAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC5C,8OAAC,4IAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,0FAA0F,EACpG,WAAW,OACP,qEACA,4CACJ;;8CAEF,8OAAC;oCAAK,WAAU;8CAAW,kHAAA,CAAA,cAAW,CAAC,KAAe;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuB;;;;;;sDACtC,8OAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;;;;;;;;;;;;gCAG7D,WAAW,sBACV,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;2BAhBd;;;;;;;;;;;;;;;;;AAuBjB", "debugId": null}}, {"offset": {"line": 849, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/UserMenu.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { UserRole } from '@/types/auth'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { User, Settings, LogOut, Shield, School, Truck, GraduationCap } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function UserMenu() {\n  const { user, profile, signOut } = useAuth()\n  const { t } = useTranslation()\n\n  if (!user || !profile) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <a href=\"/auth\">{t('auth.login')}</a>\n      </Button>\n    )\n  }\n\n  const getRoleIcon = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return <Shield className=\"h-4 w-4\" />\n      case UserRole.SCHOOL:\n        return <School className=\"h-4 w-4\" />\n      case UserRole.DELIVERY:\n        return <Truck className=\"h-4 w-4\" />\n      case UserRole.STUDENT:\n        return <GraduationCap className=\"h-4 w-4\" />\n      default:\n        return <User className=\"h-4 w-4\" />\n    }\n  }\n\n  const getRoleLabel = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'مدير'\n      case UserRole.SCHOOL:\n        return 'مدرسة'\n      case UserRole.DELIVERY:\n        return 'شريك توصيل'\n      case UserRole.STUDENT:\n        return 'طالب'\n      default:\n        return 'مستخدم'\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n    // إعادة توجيه إلى الصفحة الرئيسية بعد تسجيل الخروج\n    window.location.href = '/'\n  }\n\n  const getDashboardUrl = () => {\n    if (!profile) return '/dashboard/student'\n\n    // توجيه كل دور إلى لوحة التحكم المخصصة له\n    switch (profile.role) {\n      case UserRole.ADMIN:\n        return '/dashboard/admin'\n      case UserRole.SCHOOL:\n        return '/dashboard/school'\n      case UserRole.DELIVERY:\n        return '/dashboard/delivery'\n      case UserRole.STUDENT:\n        return '/dashboard/student'\n      default:\n        return '/dashboard/student'\n    }\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <User className=\"h-[1.2rem] w-[1.2rem]\" />\n          <span className=\"sr-only\">User menu</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">\n              {profile.full_name}\n            </p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n              {getRoleIcon(profile.role)}\n              <span>{getRoleLabel(profile.role)}</span>\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\" className=\"cursor-pointer flex items-center\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.profile')}</span>\n          </Link>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem asChild>\n          <Link href={getDashboardUrl()} className=\"cursor-pointer flex items-center\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.dashboard')}</span>\n          </Link>\n        </DropdownMenuItem>\n        \n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem \n          className=\"cursor-pointer text-red-600 focus:text-red-600\"\n          onClick={handleSignOut}\n        >\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>{t('auth.logout')}</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAhBA;;;;;;;;;AAkBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,8OAAC;gBAAE,MAAK;0BAAS,EAAE;;;;;;;;;;;IAGzB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,mDAAmD;QACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO;QAErB,0CAA0C;QAC1C,OAAQ,QAAQ,IAAI;YAClB,KAAK,oHAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,oHAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,QAAQ,SAAS;;;;;;8CAEpB,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAI,WAAU;;wCACZ,YAAY,QAAQ,IAAI;sDACzB,8OAAC;sDAAM,aAAa,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAItC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;;8CAC9B,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM;4BAAmB,WAAU;;8CACvC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS;;0CAET,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { useNotifications, getNotificationIcon, getNotificationColor, formatNotificationTime } from '@/contexts/NotificationContext'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { Separator } from '@/components/ui/separator'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { \n  Bell,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  Settings,\n  X\n} from 'lucide-react'\n\nexport function NotificationDropdown() {\n  const { \n    notifications, \n    unreadCount, \n    markAsRead, \n    markAllAsRead, \n    removeNotification, \n    clearAll \n  } = useNotifications()\n  \n  const [isOpen, setIsOpen] = useState(false)\n\n  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {\n    markAsRead(notificationId)\n    if (actionUrl) {\n      window.location.href = actionUrl\n    }\n  }\n\n  const recentNotifications = notifications.slice(0, 10)\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      \n      <DropdownMenuContent \n        align=\"end\" \n        className=\"w-80 p-0\"\n        sideOffset={5}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h3 className=\"font-semibold arabic-text\">الإشعارات</h3>\n          <div className=\"flex items-center gap-2\">\n            {unreadCount > 0 && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={markAllAsRead}\n                className=\"h-8 px-2 text-xs arabic-text\"\n              >\n                <CheckCheck className=\"h-3 w-3 mr-1\" />\n                قراءة الكل\n              </Button>\n            )}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-8 w-8 p-0\"\n            >\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Notifications List */}\n        <ScrollArea className=\"h-96\">\n          {recentNotifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center py-8 text-center\">\n              <Bell className=\"h-12 w-12 text-gray-400 mb-3\" />\n              <p className=\"text-gray-500 arabic-text\">لا توجد إشعارات</p>\n              <p className=\"text-sm text-gray-400 arabic-text\">ستظهر إشعاراتك هنا</p>\n            </div>\n          ) : (\n            <div className=\"divide-y\">\n              {recentNotifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${\n                    !notification.isRead ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''\n                  }`}\n                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    {/* Icon */}\n                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm ${\n                      getNotificationColor(notification.priority)\n                    }`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between\">\n                        <h4 className={`text-sm font-medium arabic-text ${\n                          !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        \n                        {/* Actions */}\n                        <div className=\"flex items-center gap-1 ml-2\">\n                          {!notification.isRead && (\n                            <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                          )}\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100 hover:text-red-600\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              removeNotification(notification.id)\n                            }}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      </div>\n\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text line-clamp-2\">\n                        {notification.message}\n                      </p>\n\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-500\">\n                          {formatNotificationTime(notification.createdAt)}\n                        </span>\n\n                        {notification.actionText && notification.actionUrl && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 px-2 text-xs text-blue-600 hover:text-blue-700 arabic-text\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              handleNotificationClick(notification.id, notification.actionUrl)\n                            }}\n                          >\n                            {notification.actionText}\n                            <ExternalLink className=\"h-3 w-3 mr-1\" />\n                          </Button>\n                        )}\n                      </div>\n\n                      {/* Expiry Warning */}\n                      {notification.expiresAt && (\n                        <div className=\"mt-2\">\n                          <Badge variant=\"outline\" className=\"text-xs arabic-text\">\n                            ينتهي في {formatNotificationTime(notification.expiresAt)}\n                          </Badge>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n\n        {/* Footer */}\n        {notifications.length > 0 && (\n          <>\n            <Separator />\n            <div className=\"p-3 flex items-center justify-between\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-xs arabic-text\"\n                asChild\n              >\n                <a href=\"/notifications\">عرض جميع الإشعارات</a>\n              </Button>\n              \n              {notifications.length > 0 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={clearAll}\n                  className=\"text-xs text-red-600 hover:text-red-700 arabic-text\"\n                >\n                  <Trash2 className=\"h-3 w-3 mr-1\" />\n                  حذف الكل\n                </Button>\n              )}\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n\n// مكون إشعار منبثق للإشعارات الهامة\ninterface ToastNotificationProps {\n  notification: {\n    id: string\n    title: string\n    message: string\n    type: string\n    priority: string\n  }\n  onClose: () => void\n  onAction?: () => void\n}\n\nexport function ToastNotification({ notification, onClose, onAction }: ToastNotificationProps) {\n  return (\n    <div className={`fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border ${\n      getNotificationColor(notification.priority as any)\n    } animate-in slide-in-from-right duration-300`}>\n      <div className=\"flex items-start gap-3\">\n        <div className=\"flex-shrink-0 text-lg\">\n          {getNotificationIcon(notification.type as any)}\n        </div>\n        \n        <div className=\"flex-1\">\n          <h4 className=\"font-medium arabic-text\">{notification.title}</h4>\n          <p className=\"text-sm mt-1 arabic-text\">{notification.message}</p>\n          \n          <div className=\"flex items-center gap-2 mt-3\">\n            {onAction && (\n              <Button size=\"sm\" onClick={onAction} className=\"arabic-text\">\n                عرض\n              </Button>\n            )}\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// مكون عداد الإشعارات البسيط\nexport function NotificationBadge() {\n  const { unreadCount } = useNotifications()\n\n  if (unreadCount === 0) return null\n\n  return (\n    <Badge \n      variant=\"destructive\" \n      className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n    >\n      {unreadCount > 99 ? '99+' : unreadCount}\n    </Badge>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;AAuBO,SAAS;IACd,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,WAAW;QACX,IAAI,WAAW;YACb,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,sBAAsB,cAAc,KAAK,CAAC,GAAG;IAEnD,qBACE,8OAAC,4IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAMpC,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;;oCACZ,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAI3C,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;iDAGnD,8OAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,6BACxB,8OAAC;oCAEC,WAAW,CAAC,6EAA6E,EACvF,CAAC,aAAa,MAAM,GAAG,sCAAsC,IAC7D;oCACF,SAAS,IAAM,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;8CAE9E,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAW,CAAC,4EAA4E,EAC3F,CAAA,GAAA,uIAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,GAC1C;0DACC,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAW,CAAC,gCAAgC,EAC9C,CAAC,aAAa,MAAM,GAAG,kCAAkC,oCACzD;0EACC,aAAa,KAAK;;;;;;0EAIrB,8OAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,8OAAC;wEAAI,WAAU;;;;;;kFAEjB,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;kFAEA,cAAA,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKnB,8OAAC;wDAAE,WAAU;kEACV,aAAa,OAAO;;;;;;kEAGvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;4DAG/C,aAAa,UAAU,IAAI,aAAa,SAAS,kBAChD,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;gEACjE;;oEAEC,aAAa,UAAU;kFACxB,8OAAC,sNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;oDAM7B,aAAa,SAAS,kBACrB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAsB;gEAC7C,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mCAvE5D,aAAa,EAAE;;;;;;;;;;;;;;;oBAoF7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,8OAAC,qIAAA,CAAA,YAAS;;;;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAE,MAAK;sDAAiB;;;;;;;;;;;oCAG1B,cAAc,MAAM,GAAG,mBACtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;AAeO,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAA0B;IAC3F,qBACE,8OAAC;QAAI,WAAW,CAAC,8DAA8D,EAC7E,CAAA,GAAA,uIAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,EAC3C,4CAA4C,CAAC;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;8BAGxC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2B,aAAa,KAAK;;;;;;sCAC3D,8OAAC;4BAAE,WAAU;sCAA4B,aAAa,OAAO;;;;;;sCAE7D,8OAAC;4BAAI,WAAU;;gCACZ,0BACC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;oCAAU,WAAU;8CAAc;;;;;;8CAI/D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,SAAS;8CACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;AAGO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEvC,IAAI,gBAAgB,GAAG,OAAO;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAU;kBAET,cAAc,KAAK,QAAQ;;;;;;AAGlC", "debugId": null}}, {"offset": {"line": 1774, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useMemo } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCart } from '@/contexts/CartContext'\nimport { useMenu } from '@/contexts/MenuContext'\nimport { NavigationSkeleton, SideElementsSkeleton } from '@/components/NavigationSkeleton'\nimport { Button } from '@/components/ui/button'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { LanguageToggle } from '@/components/language-toggle'\nimport { UserMenu } from '@/components/auth/UserMenu'\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  GraduationCap,\n  Home,\n  ShoppingBag,\n  Palette,\n  Info,\n  Phone,\n  Search,\n  Heart,\n  ExternalLink,\n  FileText,\n  Link as LinkIcon,\n  ChevronDown,\n  Grid3X3,\n  Settings,\n  Package,\n  Calendar,\n  ShoppingCart\n} from 'lucide-react'\n\n// أنواع البيانات لعناصر القائمة\ninterface MenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n}\n\n// نوع عنصر القائمة المعروض\ninterface NavItem {\n  id: string\n  href: string\n  label: string\n  icon?: React.ReactElement\n  target_type: 'internal' | 'external' | 'page'\n  subItems?: {\n    id: string\n    href: string\n    label: string\n    target_type: 'internal' | 'external' | 'page'\n  }[]\n}\n\nexport function Navigation() {\n  const { t, locale } = useTranslation()\n  const { cartCount, wishlistCount } = useCart()\n  const { menuItems, loading } = useMenu()\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [isTransitioning, setIsTransitioning] = useState(false)\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false)\n  }, [pathname])\n\n  // Handle transition state when loading changes\n  useEffect(() => {\n    if (!loading && menuItems.length > 0) {\n      setIsTransitioning(true)\n      const timer = setTimeout(() => {\n        setIsTransitioning(false)\n      }, 300) // Short delay for smooth transition\n      return () => clearTimeout(timer)\n    }\n  }, [loading, menuItems.length])\n\n\n\n\n\n  // تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون مع تحسين الأداء\n  const getNavItemsFromDB = useMemo(() => {\n    // فلترة العناصر الرئيسية فقط (التي ليس لها parent_id) والمفعلة\n    const mainItems = menuItems\n      .filter(item => !item.parent_id && item.is_active)\n      .sort((a, b) => a.order_index - b.order_index) // ترتيب العناصر الرئيسية\n\n    return mainItems.map(item => {\n      // اختيار العنوان حسب اللغة الحالية\n      let label = item.title_ar // افتراضي\n      if (locale === 'en' && item.title_en) {\n        label = item.title_en\n      } else if (locale === 'fr' && item.title_fr) {\n        label = item.title_fr\n      }\n\n      // تحديد الرابط حسب نوع الهدف\n      let href = item.target_value\n      if (item.target_type === 'page') {\n        href = `/pages/${item.target_value}`\n      }\n\n      // تحديد الأيقونة\n      let icon = <LinkIcon className=\"h-4 w-4\" />\n      if (item.icon) {\n        // يمكن إضافة منطق لتحويل اسم الأيقونة إلى مكون\n        switch (item.icon) {\n          case 'Home':\n            icon = <Home className=\"h-4 w-4\" />\n            break\n          case 'ShoppingBag':\n            icon = <ShoppingBag className=\"h-4 w-4\" />\n            break\n          case 'Palette':\n            icon = <Palette className=\"h-4 w-4\" />\n            break\n          case 'Search':\n            icon = <Search className=\"h-4 w-4\" />\n            break\n          case 'Info':\n            icon = <Info className=\"h-4 w-4\" />\n            break\n          case 'Phone':\n            icon = <Phone className=\"h-4 w-4\" />\n            break\n          case 'Grid3X3':\n            icon = <Grid3X3 className=\"h-4 w-4\" />\n            break\n          case 'ExternalLink':\n            icon = <ExternalLink className=\"h-4 w-4\" />\n            break\n          case 'FileText':\n            icon = <FileText className=\"h-4 w-4\" />\n            break\n          case 'Settings':\n            icon = <Settings className=\"h-4 w-4\" />\n            break\n          case 'Package':\n            icon = <Package className=\"h-4 w-4\" />\n            break\n          case 'Calendar':\n            icon = <Calendar className=\"h-4 w-4\" />\n            break\n          case 'ShoppingCart':\n            icon = <ShoppingCart className=\"h-4 w-4\" />\n            break\n          default:\n            icon = <LinkIcon className=\"h-4 w-4\" />\n        }\n      }\n\n      // البحث عن القوائم الفرعية لهذا العنصر مع الترتيب\n      const subItems = menuItems\n        .filter(subItem => subItem.parent_id === item.id && subItem.is_active)\n        .sort((a, b) => a.order_index - b.order_index) // ترتيب العناصر الفرعية\n        .map(subItem => {\n          let subLabel = subItem.title_ar\n          if (locale === 'en' && subItem.title_en) {\n            subLabel = subItem.title_en\n          } else if (locale === 'fr' && subItem.title_fr) {\n            subLabel = subItem.title_fr\n          }\n\n          let subHref = subItem.target_value\n          if (subItem.target_type === 'page') {\n            subHref = `/pages/${subItem.target_value}`\n          }\n\n          return {\n            id: subItem.id,\n            href: subHref,\n            label: subLabel,\n            target_type: subItem.target_type\n          }\n        })\n\n      return {\n        id: item.id,\n        href,\n        label,\n        icon,\n        target_type: item.target_type,\n        subItems: subItems.length > 0 ? subItems : undefined\n      }\n    })\n  }, [menuItems, locale])\n\n  // القائمة الافتراضية (للاستخدام في حالة عدم وجود عناصر من قاعدة البيانات)\n  const defaultNavItems: NavItem[] = [\n    {\n      id: 'default-home',\n      href: '/',\n      label: t('navigation.home'),\n      icon: <Home className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      id: 'default-catalog',\n      href: '/catalog',\n      label: t('navigation.catalog'),\n      icon: <ShoppingBag className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      id: 'default-about',\n      href: '/about',\n      label: t('navigation.about') || 'من نحن',\n      icon: <Info className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      id: 'default-contact',\n      href: '/contact',\n      label: t('navigation.contact') || 'تواصل معنا',\n      icon: <Phone className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    }\n  ]\n\n  // استخدام عناصر القائمة من قاعدة البيانات أو الافتراضية\n  const { user, profile } = useAuth()\n\n  // تحديد عناصر القائمة بناءً على الحالة مع تحسين الاستقرار\n  const allNavItems: NavItem[] = useMemo(() => {\n    if (loading || isTransitioning) {\n      // أثناء التحميل أو الانتقال، لا نعرض أي عناصر (سيتم عرض skeleton)\n      return []\n    } else if (menuItems.length > 0) {\n      // إذا كانت هناك عناصر من قاعدة البيانات، استخدمها\n      return getNavItemsFromDB\n    } else {\n      // إذا لم تكن هناك عناصر من قاعدة البيانات، استخدم القائمة الافتراضية\n      return defaultNavItems\n    }\n  }, [loading, isTransitioning, menuItems.length, getNavItemsFromDB])\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <header className=\"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-3\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center gap-3 hover:opacity-80 transition-all duration-300 group\"\n          >\n            <div className=\"relative\">\n              <GraduationCap className=\"h-9 w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300\" />\n              <div className=\"absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white leading-tight\">\n                Graduation Toqs\n              </span>\n              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"nav-container\">\n            {(loading || isTransitioning) ? (\n              <NavigationSkeleton />\n            ) : (\n              <nav className=\"hidden lg:flex items-center gap-1\" role=\"navigation\" aria-label=\"القائمة الرئيسية\">\n                {allNavItems.map((item, index) => {\n                  // تحديد ما إذا كان الرابط خارجي\n                  const isExternal = item.target_type === 'external'\n                  const hasSubItems = item.subItems && item.subItems.length > 0\n\n                  // إذا كان العنصر له قوائم فرعية\n                  if (hasSubItems) {\n                    return (\n                      <div\n                        key={item.id}\n                        className={`relative group nav-item-enter nav-stagger-${Math.min(index + 1, 6)}`}\n                      >\n                        <button\n                          className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                            isActive(item.href)\n                              ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                              : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                          }`}\n                          aria-label={`${item.label} - قائمة فرعية`}\n                          aria-haspopup=\"true\"\n                          aria-expanded=\"false\"\n                        >\n                          <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                            {item.icon}\n                          </span>\n                          <span className=\"text-sm font-medium\">\n                            {item.label}\n                          </span>\n                          <ChevronDown className=\"h-3 w-3 transition-transform group-hover:rotate-180\" />\n                        </button>\n\n                        {/* القائمة الفرعية */}\n                        <div\n                          className=\"absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\"\n                          role=\"menu\"\n                          aria-label={`قائمة ${item.label} الفرعية`}\n                        >\n                          <div className=\"py-2\">\n                            {item.subItems?.map((subItem) => {\n                              const subIsExternal = subItem.target_type === 'external'\n                              const subLinkProps = subIsExternal\n                                ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                                : { href: subItem.href }\n\n                              return (\n                                <Link\n                                  key={subItem.id}\n                                  {...subLinkProps}\n                                  className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                                >\n                                  {subItem.label}\n                                  {subIsExternal && (\n                                    <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                                  )}\n                                </Link>\n                              )\n                            })}\n                          </div>\n                        </div>\n                      </div>\n                    )\n                  }\n\n                  // العناصر العادية بدون قوائم فرعية\n                  const linkProps = isExternal\n                    ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                    : { href: item.href }\n\n                  return (\n                <Link\n                  key={item.id}\n                  {...linkProps}\n                  className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 nav-item-enter nav-stagger-${Math.min(index + 1, 6)} ${\n                    isActive(item.href)\n                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                  }`}\n                >\n                  <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                    {item.icon}\n                  </span>\n                  <span className=\"text-sm font-medium\">\n                    {item.label}\n                  </span>\n                  {isExternal && (\n                    <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                  )}\n                  {isActive(item.href) && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full\"></div>\n                  )}\n                </Link>\n              )\n            })}\n              </nav>\n            )}\n          </div>\n\n          {/* Desktop Actions */}\n          {(loading || isTransitioning) ? (\n            <SideElementsSkeleton />\n          ) : (\n            <div className=\"hidden lg:flex items-center gap-2\">\n              {/* Wishlist */}\n              <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/wishlist\">\n                <Heart className=\"h-5 w-5 transition-colors group-hover:text-red-500\" />\n                {wishlistCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse\"\n                  >\n                    {wishlistCount > 99 ? '99+' : wishlistCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Cart Icon */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5 transition-colors group-hover:text-blue-600\" />\n                {cartCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse\"\n                  >\n                    {cartCount > 99 ? '99+' : cartCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Notifications */}\n            <NotificationDropdown />\n\n            {/* Divider */}\n            <div className=\"h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n\n            {/* Language & Theme Controls */}\n            <div className=\"flex items-center gap-1\">\n              <LanguageToggle />\n              <ThemeToggle />\n            </div>\n\n            {/* User Menu */}\n            <UserMenu />\n            </div>\n          )}\n\n          {/* Mobile Actions */}\n          <div className=\"flex lg:hidden items-center gap-2\">\n            {/* Mobile Cart */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5\" />\n                {cartCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600\"\n                  >\n                    {cartCount > 9 ? '9+' : cartCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"relative\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              <div className=\"relative w-6 h-6 flex items-center justify-center\">\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${\n                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'\n                  }`}\n                />\n              </div>\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div\n          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <div className=\"border-t border-gray-200 dark:border-gray-700 py-4\">\n            {(loading || isTransitioning) ? (\n              <NavigationSkeleton isMobile={true} />\n            ) : (\n              <nav className=\"flex flex-col gap-1 mb-6\">\n                {allNavItems.map((item, index) => {\n                // تحديد ما إذا كان الرابط خارجي\n                const isExternal = item.target_type === 'external'\n                const hasSubItems = item.subItems && item.subItems.length > 0\n\n                // إذا كان العنصر له قوائم فرعية\n                if (hasSubItems) {\n                  return (\n                    <div key={item.id} className=\"mx-2\">\n                      <div\n                        className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 font-medium ${\n                          isActive(item.href)\n                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                            : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                        }`}\n                        style={{\n                          animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                          animationDuration: '0.3s',\n                          animationTimingFunction: 'ease-out',\n                          animationFillMode: 'forwards',\n                          animationDelay: `${index * 50}ms`\n                        }}\n                      >\n                        <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                          {item.icon}\n                        </span>\n                        <span className=\"text-sm flex-1\">\n                          {item.label}\n                        </span>\n                        <ChevronDown className=\"h-4 w-4\" />\n                      </div>\n\n                      {/* القوائم الفرعية للموبايل */}\n                      <div className=\"ml-6 mt-2 space-y-1\">\n                        {item.subItems?.map((subItem) => {\n                          const subIsExternal = subItem.target_type === 'external'\n                          const subLinkProps = subIsExternal\n                            ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }\n                            : { href: subItem.href }\n\n                          return (\n                            <Link\n                              key={subItem.id}\n                              {...subLinkProps}\n                              onClick={() => setIsMobileMenuOpen(false)}\n                              className=\"flex items-center gap-2 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors\"\n                            >\n                              {subItem.label}\n                              {subIsExternal && (\n                                <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                              )}\n                            </Link>\n                          )\n                        })}\n                      </div>\n                    </div>\n                  )\n                }\n\n                // العناصر العادية بدون قوائم فرعية\n                const linkProps = isExternal\n                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                  : { href: item.href }\n\n                return (\n                  <Link\n                    key={item.id}\n                    {...linkProps}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 font-medium ${\n                      isActive(item.href)\n                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                    }`}\n                    style={{\n                      animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                      animationDuration: '0.3s',\n                      animationTimingFunction: 'ease-out',\n                      animationFillMode: 'forwards',\n                      animationDelay: `${index * 50}ms`\n                    }}\n                  >\n                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                      {item.icon}\n                    </span>\n                    <span className=\"text-sm\">\n                      {item.label}\n                    </span>\n                    {isExternal && (\n                      <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                    )}\n                  </Link>\n                )\n              })}\n              </nav>\n            )}\n\n            {/* Mobile Actions */}\n            <div className=\"flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center gap-2\">\n                <LanguageToggle />\n                <ThemeToggle />\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n                  <Link href=\"/wishlist\">\n                    <Heart className=\"h-5 w-5\" />\n                    {wishlistCount > 0 && (\n                      <Badge\n                        variant=\"destructive\"\n                        className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs\"\n                      >\n                        {wishlistCount > 9 ? '9+' : wishlistCount}\n                      </Badge>\n                    )}\n                  </Link>\n                </Button>\n                <UserMenu />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport { Navigation }\nexport default Navigation\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAhBA;;;;;;;;;;;;;;;;;AAkEO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;IACtB,GAAG;QAAC;KAAS;IAEb,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,UAAU,MAAM,GAAG,GAAG;YACpC,mBAAmB;YACnB,MAAM,QAAQ,WAAW;gBACvB,mBAAmB;YACrB,GAAG,KAAK,oCAAoC;;YAC5C,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAS,UAAU,MAAM;KAAC;IAM9B,yEAAyE;IACzE,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAChC,+DAA+D;QAC/D,MAAM,YAAY,UACf,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS,EAChD,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW,EAAE,yBAAyB;;QAE1E,OAAO,UAAU,GAAG,CAAC,CAAA;YACnB,mCAAmC;YACnC,IAAI,QAAQ,KAAK,QAAQ,CAAC,UAAU;;YACpC,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBACpC,QAAQ,KAAK,QAAQ;YACvB,OAAO,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBAC3C,QAAQ,KAAK,QAAQ;YACvB;YAEA,6BAA6B;YAC7B,IAAI,OAAO,KAAK,YAAY;YAC5B,IAAI,KAAK,WAAW,KAAK,QAAQ;gBAC/B,OAAO,CAAC,OAAO,EAAE,KAAK,YAAY,EAAE;YACtC;YAEA,iBAAiB;YACjB,IAAI,qBAAO,8OAAC,kMAAA,CAAA,OAAQ;gBAAC,WAAU;;;;;;YAC/B,IAAI,KAAK,IAAI,EAAE;gBACb,+CAA+C;gBAC/C,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,qBAAO,8OAAC,mMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC9B;oBACF,KAAK;wBACH,qBAAO,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACzB;oBACF,KAAK;wBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACxB;oBACF,KAAK;wBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B;oBACF,KAAK;wBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC3B;oBACF,KAAK;wBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC3B;oBACF,KAAK;wBACH,qBAAO,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC3B;oBACF,KAAK;wBACH,qBAAO,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B;oBACF;wBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAQ;4BAAC,WAAU;;;;;;gBAC/B;YACF;YAEA,kDAAkD;YAClD,MAAM,WAAW,UACd,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK,KAAK,EAAE,IAAI,QAAQ,SAAS,EACpE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW,EAAE,wBAAwB;aACtE,GAAG,CAAC,CAAA;gBACH,IAAI,WAAW,QAAQ,QAAQ;gBAC/B,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBACvC,WAAW,QAAQ,QAAQ;gBAC7B,OAAO,IAAI,WAAW,QAAQ,QAAQ,QAAQ,EAAE;oBAC9C,WAAW,QAAQ,QAAQ;gBAC7B;gBAEA,IAAI,UAAU,QAAQ,YAAY;gBAClC,IAAI,QAAQ,WAAW,KAAK,QAAQ;oBAClC,UAAU,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;gBAC5C;gBAEA,OAAO;oBACL,IAAI,QAAQ,EAAE;oBACd,MAAM;oBACN,OAAO;oBACP,aAAa,QAAQ,WAAW;gBAClC;YACF;YAEF,OAAO;gBACL,IAAI,KAAK,EAAE;gBACX;gBACA;gBACA;gBACA,aAAa,KAAK,WAAW;gBAC7B,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;YAC7C;QACF;IACF,GAAG;QAAC;QAAW;KAAO;IAEtB,0EAA0E;IAC1E,MAAM,kBAA6B;QACjC;YACE,IAAI;YACJ,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO,EAAE,uBAAuB;YAChC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO,EAAE,yBAAyB;YAClC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;QACf;KACD;IAED,wDAAwD;IACxD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,0DAA0D;IAC1D,MAAM,cAAyB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACrC,IAAI,WAAW,iBAAiB;YAC9B,kEAAkE;YAClE,OAAO,EAAE;QACX,OAAO,IAAI,UAAU,MAAM,GAAG,GAAG;YAC/B,kDAAkD;YAClD,OAAO;QACT,OAAO;YACL,qEAAqE;YACrE,OAAO;QACT;IACF,GAAG;QAAC;QAAS;QAAiB,UAAU,MAAM;QAAE;KAAkB;IAElE,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgE;;;;;;sDAGhF,8OAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,sBAAsB,WAAW,OAAO,sCAAsC;;;;;;;;;;;;;;;;;;sCAMvG,8OAAC;4BAAI,WAAU;sCACZ,AAAC,WAAW,gCACX,8OAAC,wIAAA,CAAA,qBAAkB;;;;qDAEnB,8OAAC;gCAAI,WAAU;gCAAoC,MAAK;gCAAa,cAAW;0CAC7E,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oCAE5D,gCAAgC;oCAChC,IAAI,aAAa;wCACf,qBACE,8OAAC;4CAEC,WAAW,CAAC,0CAA0C,EAAE,KAAK,GAAG,CAAC,QAAQ,GAAG,IAAI;;8DAEhF,8OAAC;oDACC,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;oDACF,cAAY,GAAG,KAAK,KAAK,CAAC,cAAc,CAAC;oDACzC,iBAAc;oDACd,iBAAc;;sEAEd,8OAAC;4DAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;sEAChH,KAAK,IAAI;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAIzB,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,cAAY,CAAC,MAAM,EAAE,KAAK,KAAK,CAAC,QAAQ,CAAC;8DAEzC,cAAA,8OAAC;wDAAI,WAAU;kEACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;4DACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;4DAC9C,MAAM,eAAe,gBACjB;gEAAE,MAAM,QAAQ,IAAI;gEAAE,QAAQ;gEAAU,KAAK;4DAAsB,IACnE;gEAAE,MAAM,QAAQ,IAAI;4DAAC;4DAEzB,qBACE,8OAAC,4JAAA,CAAA,UAAI;gEAEF,GAAG,YAAY;gEAChB,WAAU;;oEAET,QAAQ,KAAK;oEACb,+BACC,8OAAC,sNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;+DANrB,QAAQ,EAAE;;;;;wDAUrB;;;;;;;;;;;;2CA/CC,KAAK,EAAE;;;;;oCAoDlB;oCAEA,mCAAmC;oCACnC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACF,8OAAC,4JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,WAAW,CAAC,iIAAiI,EAAE,KAAK,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,EACrK,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;0DAEF,8OAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;0DAChH,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAEzB,SAAS,KAAK,IAAI,mBACjB,8OAAC;gDAAI,WAAU;;;;;;;uCAlBZ,KAAK,EAAE;;;;;gCAsBlB;;;;;;;;;;;wBAMA,WAAW,gCACX,8OAAC,wIAAA,CAAA,uBAAoB;;;;iDAErB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CACpE,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOtC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,YAAY,mBACX,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,YAAY,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOlC,8OAAC,2JAAA,CAAA,uBAAoB;;;;;8CAGrB,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wIAAA,CAAA,iBAAc;;;;;sDACf,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;8CAId,8OAAC,sIAAA,CAAA,WAAQ;;;;;;;;;;;sCAKX,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAW,OAAO;8CAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,YAAY,mBACX,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,YAAY,IAAI,OAAO;;;;;;;;;;;;;;;;;8CAOhC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEpC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,cAAc,oBACjC;;;;;;0DAEJ,8OAAC;gDACC,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,cAAc,eACjC;;;;;;0DAEJ,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,eAAe,mBAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,8OAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,mBAAmB,6BAA6B,qBAChD;8BAEF,cAAA,8OAAC;wBAAI,WAAU;;4BACX,WAAW,gCACX,8OAAC,wIAAA,CAAA,qBAAkB;gCAAC,UAAU;;;;;qDAE9B,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACxB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;oCAE5D,gCAAgC;oCAChC,IAAI,aAAa;wCACf,qBACE,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC;oDACC,WAAW,CAAC,qFAAqF,EAC/F,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;oDACF,OAAO;wDACL,eAAe,mBAAmB,qBAAqB;wDACvD,mBAAmB;wDACnB,yBAAyB;wDACzB,mBAAmB;wDACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;oDACnC;;sEAEA,8OAAC;4DAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;sEAC3F,KAAK,IAAI;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;sEAEb,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAIzB,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,EAAE,IAAI,CAAC;wDACnB,MAAM,gBAAgB,QAAQ,WAAW,KAAK;wDAC9C,MAAM,eAAe,gBACjB;4DAAE,MAAM,QAAQ,IAAI;4DAAE,QAAQ;4DAAU,KAAK;wDAAsB,IACnE;4DAAE,MAAM,QAAQ,IAAI;wDAAC;wDAEzB,qBACE,8OAAC,4JAAA,CAAA,UAAI;4DAEF,GAAG,YAAY;4DAChB,SAAS,IAAM,oBAAoB;4DACnC,WAAU;;gEAET,QAAQ,KAAK;gEACb,+BACC,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;2DAPrB,QAAQ,EAAE;;;;;oDAWrB;;;;;;;2CA7CM,KAAK,EAAE;;;;;oCAiDrB;oCAEA,mCAAmC;oCACnC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,0FAA0F,EACpG,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;wCACF,OAAO;4CACL,eAAe,mBAAmB,qBAAqB;4CACvD,mBAAmB;4CACnB,yBAAyB;4CACzB,mBAAmB;4CACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;wCACnC;;0DAEA,8OAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;0DAC3F,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCAvBrB,KAAK,EAAE;;;;;gCA2BlB;;;;;;0CAKF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wIAAA,CAAA,iBAAc;;;;;0DACf,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;gDAAW,OAAO;0DAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;sEAET,gBAAgB,IAAI,OAAO;;;;;;;;;;;;;;;;;0DAKpC,8OAAC,sIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;;uCAGe", "debugId": null}}, {"offset": {"line": 2829, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Footer.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from 'next/link'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { \n  GraduationCap, \n  Mail, \n  Phone, \n  MapPin,\n  Facebook,\n  Twitter,\n  Instagram,\n  Linkedin,\n  Heart\n} from 'lucide-react'\n\nexport function Footer() {\n  const { t } = useTranslation()\n\n  const currentYear = new Date().getFullYear()\n\n  const footerLinks = {\n    company: [\n      { href: '/about', label: 'من نحن' },\n      { href: '/contact', label: 'تواصل معنا' },\n      { href: '/support', label: 'الدعم الفني' },\n      { href: '/privacy', label: 'سياسة الخصوصية' }\n    ],\n    services: [\n      { href: '/catalog', label: 'الكتالوج' },\n      { href: '/customize', label: 'التخصيص' },\n      { href: '/track-order', label: 'تتبع الطلب' },\n      { href: '/size-guide', label: 'دليل المقاسات' }\n    ],\n    support: [\n      { href: '/faq', label: 'الأسئلة الشائعة' },\n      { href: '/terms-conditions', label: 'الشروط والأحكام' },\n      { href: '/privacy-policy', label: 'سياسة الخصوصية' },\n      { href: '/support', label: 'الدعم الفني' }\n    ]\n  }\n\n  const socialLinks = [\n    { href: '#', icon: Facebook, label: 'Facebook' },\n    { href: '#', icon: Twitter, label: 'Twitter' },\n    { href: '#', icon: Instagram, label: 'Instagram' },\n    { href: '#', icon: Linkedin, label: 'LinkedIn' }\n  ]\n\n  return (\n    <footer className=\"bg-gray-900 text-white mt-16\">\n      <div className=\"container mx-auto px-4 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2\">\n              <GraduationCap className=\"h-8 w-8 text-blue-400\" />\n              <span className=\"text-xl font-bold\">Graduation Toqs</span>\n            </div>\n            <p className=\"text-gray-300 arabic-text leading-relaxed\">\n              أول منصة مغربية متخصصة في تأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي\n            </p>\n            \n            {/* Contact Info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2 text-sm\">\n                <Mail className=\"h-4 w-4 text-blue-400\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center gap-2 text-sm\">\n                <Phone className=\"h-4 w-4 text-blue-400\" />\n                <span>+212 6 12 34 56 78</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-sm\">\n                <MapPin className=\"h-4 w-4 text-blue-400\" />\n                <span className=\"arabic-text\">بني ملال، المغرب</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4 arabic-text\">الشركة</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.company.map((link) => (\n                <li key={link.href}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-300 hover:text-blue-400 transition-colors arabic-text\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4 arabic-text\">الخدمات</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.services.map((link) => (\n                <li key={link.href}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-300 hover:text-blue-400 transition-colors arabic-text\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4 arabic-text\">الدعم</h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.support.map((link) => (\n                <li key={link.href}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-300 hover:text-blue-400 transition-colors arabic-text\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Social Media & Copyright */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center gap-4\">\n            {/* Social Links */}\n            <div className=\"flex items-center gap-4\">\n              <span className=\"text-gray-400 arabic-text\">تابعنا على:</span>\n              {socialLinks.map((social) => {\n                const Icon = social.icon\n                return (\n                  <a\n                    key={social.label}\n                    href={social.href}\n                    className=\"text-gray-400 hover:text-blue-400 transition-colors\"\n                    aria-label={social.label}\n                  >\n                    <Icon className=\"h-5 w-5\" />\n                  </a>\n                )\n              })}\n            </div>\n\n            {/* Copyright */}\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-gray-400 text-sm arabic-text\">\n                © {currentYear} Graduation Toqs. جميع الحقوق محفوظة\n              </p>\n              <p className=\"text-gray-500 text-xs mt-1 flex items-center justify-center md:justify-end gap-1\">\n                <span className=\"arabic-text\">صُنع بـ</span>\n                <Heart className=\"h-3 w-3 text-red-500\" />\n                <span className=\"arabic-text\">في المغرب</span>\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE3B,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAU,OAAO;YAAS;YAClC;gBAAE,MAAM;gBAAY,OAAO;YAAa;YACxC;gBAAE,MAAM;gBAAY,OAAO;YAAc;YACzC;gBAAE,MAAM;gBAAY,OAAO;YAAiB;SAC7C;QACD,UAAU;YACR;gBAAE,MAAM;gBAAY,OAAO;YAAW;YACtC;gBAAE,MAAM;gBAAc,OAAO;YAAU;YACvC;gBAAE,MAAM;gBAAgB,OAAO;YAAa;YAC5C;gBAAE,MAAM;gBAAe,OAAO;YAAgB;SAC/C;QACD,SAAS;YACP;gBAAE,MAAM;gBAAQ,OAAO;YAAkB;YACzC;gBAAE,MAAM;gBAAqB,OAAO;YAAkB;YACtD;gBAAE,MAAM;gBAAmB,OAAO;YAAiB;YACnD;gBAAE,MAAM;gBAAY,OAAO;YAAc;SAC1C;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAK,MAAM,0MAAA,CAAA,WAAQ;YAAE,OAAO;QAAW;QAC/C;YAAE,MAAM;YAAK,MAAM,wMAAA,CAAA,UAAO;YAAE,OAAO;QAAU;QAC7C;YAAE,MAAM;YAAK,MAAM,4MAAA,CAAA,YAAS;YAAE,OAAO;QAAY;QACjD;YAAE,MAAM;YAAK,MAAM,0MAAA,CAAA,WAAQ;YAAE,OAAO;QAAW;KAChD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA4C;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;sCAMpC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAG,WAAU;8CACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA4B;;;;;;oCAC3C,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,OAAO,OAAO,IAAI;wCACxB,qBACE,8OAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,WAAU;4CACV,cAAY,OAAO,KAAK;sDAExB,cAAA,8OAAC;gDAAK,WAAU;;;;;;2CALX,OAAO,KAAK;;;;;oCAQvB;;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;4CAAoC;4CAC5C;4CAAY;;;;;;;kDAEjB,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;gDAAK,WAAU;0DAAc;;;;;;0DAC9B,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C", "debugId": null}}, {"offset": {"line": 3301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/layouts/PageLayout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { ReactNode } from 'react'\nimport { Navigation } from '@/components/Navigation'\nimport { Footer } from '@/components/Footer'\n\ninterface PageLayoutProps {\n  children: ReactNode\n  className?: string\n  showFooter?: boolean\n  containerClassName?: string\n}\n\nexport function PageLayout({ \n  children, \n  className = \"\", \n  showFooter = true,\n  containerClassName = \"container mx-auto px-4 py-8\"\n}: PageLayoutProps) {\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>\n      {/* Header Navigation - موحد عبر جميع الصفحات */}\n      <Navigation />\n      \n      {/* Main Content */}\n      <main className={containerClassName}>\n        {children}\n      </main>\n      \n      {/* Footer - اختياري */}\n      {showFooter && <Footer />}\n    </div>\n  )\n}\n\n// Layout خاص بلوحات التحكم\nexport function DashboardLayout({ \n  children, \n  className = \"\",\n  title,\n  description \n}: PageLayoutProps & { title?: string; description?: string }) {\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>\n      <Navigation />\n      \n      <main className=\"container mx-auto px-4 py-8\">\n        {(title || description) && (\n          <div className=\"mb-8\">\n            {title && (\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white arabic-text mb-2\">\n                {title}\n              </h1>\n            )}\n            {description && (\n              <p className=\"text-gray-600 dark:text-gray-300 arabic-text\">\n                {description}\n              </p>\n            )}\n          </div>\n        )}\n        {children}\n      </main>\n    </div>\n  )\n}\n\n// Layout للصفحات الخاصة (مثل 404، خطأ)\nexport function ErrorLayout({ children, className = \"\" }: PageLayoutProps) {\n  return (\n    <div className={`min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}>\n      <Navigation />\n      <main className=\"container mx-auto px-4 py-8 flex items-center justify-center min-h-[calc(100vh-200px)]\">\n        {children}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AAJA;;;;AAaO,SAAS,WAAW,EACzB,QAAQ,EACR,YAAY,EAAE,EACd,aAAa,IAAI,EACjB,qBAAqB,6BAA6B,EAClC;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAC,yHAAyH,EAAE,WAAW;;0BAErJ,8OAAC,gIAAA,CAAA,aAAU;;;;;0BAGX,8OAAC;gBAAK,WAAW;0BACd;;;;;;YAIF,4BAAc,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAG5B;AAGO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,YAAY,EAAE,EACd,KAAK,EACL,WAAW,EACgD;IAC3D,qBACE,8OAAC;QAAI,WAAW,CAAC,yHAAyH,EAAE,WAAW;;0BACrJ,8OAAC,gIAAA,CAAA,aAAU;;;;;0BAEX,8OAAC;gBAAK,WAAU;;oBACb,CAAC,SAAS,WAAW,mBACpB,8OAAC;wBAAI,WAAU;;4BACZ,uBACC,8OAAC;gCAAG,WAAU;0CACX;;;;;;4BAGJ,6BACC,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;oBAKR;;;;;;;;;;;;;AAIT;AAGO,SAAS,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;IACvE,qBACE,8OAAC;QAAI,WAAW,CAAC,wHAAwH,EAAE,WAAW;;0BACpJ,8OAAC,gIAAA,CAAA,aAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 3423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { \n  GraduationCap, \n  Sparkles, \n  ArrowRight, \n  Play,\n  Users,\n  School,\n  Star,\n  ChevronDown\n} from \"lucide-react\"\n\nexport function HeroSection() {\n  const { t } = useTranslation()\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    setIsVisible(true)\n  }, [])\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Gradient */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\" />\n      \n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute top-20 left-10 w-20 h-20 bg-blue-200 dark:bg-blue-800 rounded-full opacity-20 animate-pulse\" />\n        <div className=\"absolute top-40 right-20 w-16 h-16 bg-purple-200 dark:bg-purple-800 rounded-full opacity-20 animate-bounce\" />\n        <div className=\"absolute bottom-20 left-20 w-24 h-24 bg-yellow-200 dark:bg-yellow-800 rounded-full opacity-20 animate-pulse\" />\n        <div className=\"absolute bottom-40 right-10 w-12 h-12 bg-green-200 dark:bg-green-800 rounded-full opacity-20 animate-bounce\" />\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 container mx-auto px-4 py-20\">\n        <div className=\"text-center max-w-5xl mx-auto\">\n          {/* Badges */}\n          <div className={`flex flex-wrap justify-center gap-3 mb-8 transition-all duration-1000 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            <Badge variant=\"secondary\" className=\"px-4 py-2 text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n              <Users className=\"w-4 h-4 mr-2\" />\n              {t('home.hero.badges.trusted')}\n            </Badge>\n            <Badge variant=\"secondary\" className=\"px-4 py-2 text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\n              <School className=\"w-4 h-4 mr-2\" />\n              {t('home.hero.badges.schools')}\n            </Badge>\n            <Badge variant=\"secondary\" className=\"px-4 py-2 text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\">\n              <Star className=\"w-4 h-4 mr-2\" />\n              {t('home.hero.badges.satisfaction')}\n            </Badge>\n          </div>\n\n          {/* Main Title */}\n          <h1 className={`text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 dark:text-white mb-6 arabic-text leading-tight transition-all duration-1000 delay-200 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            <span className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent\">\n              {t('home.hero.title')}\n            </span>\n          </h1>\n\n          {/* Subtitle */}\n          <p className={`text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-4xl mx-auto arabic-text leading-relaxed transition-all duration-1000 delay-400 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            {t('home.hero.subtitle')}\n          </p>\n\n          {/* Description */}\n          <p className={`text-lg text-gray-500 dark:text-gray-400 mb-12 max-w-3xl mx-auto arabic-text leading-relaxed transition-all duration-1000 delay-600 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            {t('home.hero.description')}\n          </p>\n\n          {/* CTA Buttons */}\n          <div className={`flex flex-col sm:flex-row gap-4 justify-center items-center mb-16 transition-all duration-1000 delay-800 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            <Button \n              size=\"lg\" \n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold arabic-text shadow-lg hover:shadow-xl transition-all duration-300 group\"\n              asChild\n            >\n              <a href=\"/customize\" className=\"flex items-center gap-2\">\n                <Sparkles className=\"w-5 h-5\" />\n                {t('home.hero.cta.primary')}\n                <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n              </a>\n            </Button>\n            \n            <Button \n              variant=\"outline\" \n              size=\"lg\" \n              className=\"border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 px-8 py-4 text-lg font-semibold arabic-text transition-all duration-300\"\n              asChild\n            >\n              <a href=\"/catalog\" className=\"flex items-center gap-2\">\n                <GraduationCap className=\"w-5 h-5\" />\n                {t('home.hero.cta.secondary')}\n              </a>\n            </Button>\n\n            <Button \n              variant=\"ghost\" \n              size=\"lg\" \n              className=\"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 px-8 py-4 text-lg font-semibold arabic-text transition-all duration-300 group\"\n            >\n              <Play className=\"w-5 h-5 mr-2 group-hover:scale-110 transition-transform\" />\n              {t('home.hero.cta.watchDemo')}\n            </Button>\n          </div>\n\n          {/* Scroll Indicator */}\n          <div className={`animate-bounce transition-all duration-1000 delay-1000 ${\n            isVisible ? 'opacity-100' : 'opacity-0'\n          }`}>\n            <ChevronDown className=\"w-8 h-8 text-gray-400 mx-auto\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Decorative Elements */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white dark:from-gray-900 to-transparent\" />\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAiBO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAW,CAAC,sEAAsE,EACrF,YAAY,8BAA8B,4BAC1C;;8CACA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAChB,EAAE;;;;;;;8CAEL,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCACjB,EAAE;;;;;;;8CAEL,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,EAAE;;;;;;;;;;;;;sCAKP,8OAAC;4BAAG,WAAW,CAAC,+IAA+I,EAC7J,YAAY,8BAA8B,4BAC1C;sCACA,cAAA,8OAAC;gCAAK,WAAU;0CACb,EAAE;;;;;;;;;;;sCAKP,8OAAC;4BAAE,WAAW,CAAC,+IAA+I,EAC5J,YAAY,8BAA8B,4BAC1C;sCACC,EAAE;;;;;;sCAIL,8OAAC;4BAAE,WAAW,CAAC,oIAAoI,EACjJ,YAAY,8BAA8B,4BAC1C;sCACC,EAAE;;;;;;sCAIL,8OAAC;4BAAI,WAAW,CAAC,yGAAyG,EACxH,YAAY,8BAA8B,4BAC1C;;8CACA,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,OAAO;8CAEP,cAAA,8OAAC;wCAAE,MAAK;wCAAa,WAAU;;0DAC7B,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,EAAE;0DACH,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAI1B,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,OAAO;8CAEP,cAAA,8OAAC;wCAAE,MAAK;wCAAW,WAAU;;0DAC3B,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CACxB,EAAE;;;;;;;;;;;;8CAIP,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,EAAE;;;;;;;;;;;;;sCAKP,8OAAC;4BAAI,WAAW,CAAC,uDAAuD,EACtE,YAAY,gBAAgB,aAC5B;sCACA,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 3733, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useStats.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\n\ninterface Stats {\n  customers: number\n  schools: number\n  orders: number\n  satisfaction: number\n}\n\ninterface UseStatsReturn {\n  stats: Stats\n  loading: boolean\n  error: string | null\n  refetch: () => void\n}\n\nexport function useStats(): UseStatsReturn {\n  const [stats, setStats] = useState<Stats>({\n    customers: 1200,\n    schools: 50,\n    orders: 2500,\n    satisfaction: 98\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const fetchStats = async () => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      // Simulate API call - replace with actual API endpoint\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      // Mock data - replace with actual API response\n      const mockStats: Stats = {\n        customers: 1200 + Math.floor(Math.random() * 100),\n        schools: 50 + Math.floor(Math.random() * 10),\n        orders: 2500 + Math.floor(Math.random() * 200),\n        satisfaction: 98\n      }\n      \n      setStats(mockStats)\n    } catch (err) {\n      setError('فشل في تحميل الإحصائيات')\n      console.error('Error fetching stats:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const refetch = () => {\n    fetchStats()\n  }\n\n  useEffect(() => {\n    fetchStats()\n  }, [])\n\n  return {\n    stats,\n    loading,\n    error,\n    refetch\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAkBO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;QACxC,WAAW;QACX,SAAS;QACT,QAAQ;QACR,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa;QACjB,WAAW;QACX,SAAS;QAET,IAAI;YACF,uDAAuD;YACvD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,+CAA+C;YAC/C,MAAM,YAAmB;gBACvB,WAAW,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAC7C,SAAS,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBACzC,QAAQ,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAC1C,cAAc;YAChB;YAEA,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 3788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { useStats } from \"@/hooks/useStats\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { \n  Users, \n  School, \n  ShoppingBag, \n  Star,\n  TrendingUp\n} from \"lucide-react\"\n\ninterface StatItemProps {\n  icon: React.ReactNode\n  number: string\n  label: string\n  delay: number\n  color: string\n}\n\nfunction StatItem({ icon, number, label, delay, color }: StatItemProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const [animatedNumber, setAnimatedNumber] = useState('0')\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n          // Animate number counting\n          const finalNumber = number.replace(/[^0-9]/g, '')\n          const numValue = parseInt(finalNumber)\n          if (!isNaN(numValue)) {\n            let current = 0\n            const increment = numValue / 50\n            const timer = setInterval(() => {\n              current += increment\n              if (current >= numValue) {\n                setAnimatedNumber(number)\n                clearInterval(timer)\n              } else {\n                setAnimatedNumber(Math.floor(current).toString() + (number.includes('+') ? '+' : '') + (number.includes('%') ? '%' : ''))\n              }\n            }, 30)\n          } else {\n            setAnimatedNumber(number)\n          }\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [number])\n\n  return (\n    <div\n      ref={ref}\n      className={`transition-all duration-1000 ${\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n      }`}\n      style={{ transitionDelay: `${delay}ms` }}\n    >\n      <Card className=\"text-center hover:shadow-lg transition-all duration-300 border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\">\n        <CardContent className=\"p-8\">\n          <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${color}`}>\n            {icon}\n          </div>\n          <div className=\"text-4xl font-bold text-gray-900 dark:text-white mb-2 arabic-text\">\n            {animatedNumber}\n          </div>\n          <div className=\"text-gray-600 dark:text-gray-300 font-medium arabic-text\">\n            {label}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nexport function StatsSection() {\n  const { t } = useTranslation()\n  const { stats: statsData, loading } = useStats()\n\n  const stats = [\n    {\n      icon: <Users className=\"w-8 h-8 text-white\" />,\n      number: loading ? '...' : `${statsData.customers.toLocaleString()}+`,\n      label: t('home.stats.customers.label'),\n      color: \"bg-gradient-to-br from-blue-500 to-blue-600\",\n      delay: 0\n    },\n    {\n      icon: <School className=\"w-8 h-8 text-white\" />,\n      number: loading ? '...' : `${statsData.schools}+`,\n      label: t('home.stats.schools.label'),\n      color: \"bg-gradient-to-br from-green-500 to-green-600\",\n      delay: 200\n    },\n    {\n      icon: <ShoppingBag className=\"w-8 h-8 text-white\" />,\n      number: loading ? '...' : `${statsData.orders.toLocaleString()}+`,\n      label: t('home.stats.orders.label'),\n      color: \"bg-gradient-to-br from-purple-500 to-purple-600\",\n      delay: 400\n    },\n    {\n      icon: <Star className=\"w-8 h-8 text-white\" />,\n      number: loading ? '...' : `${statsData.satisfaction}%`,\n      label: t('home.stats.satisfaction.label'),\n      color: \"bg-gradient-to-br from-yellow-500 to-yellow-600\",\n      delay: 600\n    }\n  ]\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-4\">\n            <TrendingUp className=\"w-4 h-4\" />\n            إحصائيات المنصة\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text\">\n            {t('home.stats.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto arabic-text\">\n            {t('home.stats.subtitle')}\n          </p>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {stats.map((stat, index) => (\n            <StatItem\n              key={index}\n              icon={stat.icon}\n              number={stat.number}\n              label={stat.label}\n              delay={stat.delay}\n              color={stat.color}\n            />\n          ))}\n        </div>\n\n        {/* Additional Info */}\n        <div className=\"text-center mt-16\">\n          <p className=\"text-gray-500 dark:text-gray-400 arabic-text\">\n            آخر تحديث: {new Date().toLocaleDateString('ar-MA')}\n          </p>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAsBA,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAiB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;gBACb,0BAA0B;gBAC1B,MAAM,cAAc,OAAO,OAAO,CAAC,WAAW;gBAC9C,MAAM,WAAW,SAAS;gBAC1B,IAAI,CAAC,MAAM,WAAW;oBACpB,IAAI,UAAU;oBACd,MAAM,YAAY,WAAW;oBAC7B,MAAM,QAAQ,YAAY;wBACxB,WAAW;wBACX,IAAI,WAAW,UAAU;4BACvB,kBAAkB;4BAClB,cAAc;wBAChB,OAAO;4BACL,kBAAkB,KAAK,KAAK,CAAC,SAAS,QAAQ,KAAK,CAAC,OAAO,QAAQ,CAAC,OAAO,MAAM,EAAE,IAAI,CAAC,OAAO,QAAQ,CAAC,OAAO,MAAM,EAAE;wBACzH;oBACF,GAAG;gBACL,OAAO;oBACL,kBAAkB;gBACpB;YACF;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,IAAI,IAAI,OAAO,EAAE;YACf,SAAS,OAAO,CAAC,IAAI,OAAO;QAC9B;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,6BAA6B,EACvC,YAAY,8BAA8B,4BAC1C;QACF,OAAO;YAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;QAAC;kBAEvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAW,CAAC,oEAAoE,EAAE,OAAO;kCAC3F;;;;;;kCAEH,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAEH,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb;AAEO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EAAE,OAAO,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAE7C,MAAM,QAAQ;QACZ;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,QAAQ,UAAU,QAAQ,GAAG,UAAU,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC;YACpE,OAAO,EAAE;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,QAAQ,UAAU,QAAQ,GAAG,UAAU,OAAO,CAAC,CAAC,CAAC;YACjD,OAAO,EAAE;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,QAAQ,UAAU,QAAQ,GAAG,UAAU,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC;YACjE,OAAO,EAAE;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,QAAQ,UAAU,QAAQ,GAAG,UAAU,YAAY,CAAC,CAAC,CAAC;YACtD,OAAO,EAAE;YACT,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGpC,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAEC,MAAM,KAAK,IAAI;4BACf,QAAQ,KAAK,MAAM;4BACnB,OAAO,KAAK,KAAK;4BACjB,OAAO,KAAK,KAAK;4BACjB,OAAO,KAAK,KAAK;2BALZ;;;;;;;;;;8BAWX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAA+C;4BAC9C,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAMtD", "debugId": null}}, {"offset": {"line": 4055, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { \n  Palette, \n  Sparkles, \n  Users, \n  GraduationCap,\n  Shield,\n  Headphones,\n  ArrowRight,\n  CheckCircle\n} from \"lucide-react\"\n\ninterface FeatureCardProps {\n  icon: React.ReactNode\n  title: string\n  description: string\n  color: string\n  delay: number\n  features?: string[]\n}\n\nfunction FeatureCard({ icon, title, description, color, delay, features }: FeatureCardProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      className={`transition-all duration-1000 ${\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n      }`}\n      style={{ transitionDelay: `${delay}ms` }}\n    >\n      <Card className=\"h-full hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 group hover:-translate-y-2\">\n        <CardHeader className=\"text-center pb-4\">\n          <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 mx-auto ${color} group-hover:scale-110 transition-transform duration-300`}>\n            {icon}\n          </div>\n          <CardTitle className=\"text-xl font-bold text-gray-900 dark:text-white arabic-text mb-2\">\n            {title}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"text-center\">\n          <CardDescription className=\"text-gray-600 dark:text-gray-300 arabic-text leading-relaxed mb-4\">\n            {description}\n          </CardDescription>\n          \n          {features && (\n            <div className=\"space-y-2 mb-4\">\n              {features.map((feature, index) => (\n                <div key={index} className=\"flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400\">\n                  <CheckCircle className=\"w-4 h-4 text-green-500 flex-shrink-0\" />\n                  <span className=\"arabic-text\">{feature}</span>\n                </div>\n              ))}\n            </div>\n          )}\n          \n          <Button \n            variant=\"ghost\" \n            size=\"sm\" \n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 group/btn\"\n          >\n            اعرف المزيد\n            <ArrowRight className=\"w-4 h-4 mr-2 group-hover/btn:translate-x-1 transition-transform\" />\n          </Button>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nexport function FeaturesShowcase() {\n  const { t } = useTranslation()\n\n  const features = [\n    {\n      icon: <Palette className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.customization.title'),\n      description: t('home.features.customization.description'),\n      color: \"bg-gradient-to-br from-purple-500 to-purple-600\",\n      delay: 0,\n      features: [\n        \"معاينة فورية ثلاثية الأبعاد\",\n        \"مكتبة ألوان واسعة\",\n        \"إكسسوارات متنوعة\"\n      ]\n    },\n    {\n      icon: <Sparkles className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.ai.title'),\n      description: t('home.features.ai.description'),\n      color: \"bg-gradient-to-br from-yellow-500 to-yellow-600\",\n      delay: 200,\n      features: [\n        \"اقتراحات ذكية مخصصة\",\n        \"تحليل الأسلوب الشخصي\",\n        \"مساعد افتراضي متقدم\"\n      ]\n    },\n    {\n      icon: <Users className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.roles.title'),\n      description: t('home.features.roles.description'),\n      color: \"bg-gradient-to-br from-green-500 to-green-600\",\n      delay: 400,\n      features: [\n        \"لوحة تحكم للطلاب\",\n        \"إدارة المدارس\",\n        \"نظام شركاء التوصيل\"\n      ]\n    },\n    {\n      icon: <GraduationCap className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.tracking.title'),\n      description: t('home.features.tracking.description'),\n      color: \"bg-gradient-to-br from-blue-500 to-blue-600\",\n      delay: 600,\n      features: [\n        \"تتبع لحظي للطلبات\",\n        \"إشعارات فورية\",\n        \"تحديثات مستمرة\"\n      ]\n    },\n    {\n      icon: <Shield className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.quality.title'),\n      description: t('home.features.quality.description'),\n      color: \"bg-gradient-to-br from-red-500 to-red-600\",\n      delay: 800,\n      features: [\n        \"أقمشة فاخرة مختارة\",\n        \"تصنيع احترافي\",\n        \"ضمان الجودة\"\n      ]\n    },\n    {\n      icon: <Headphones className=\"w-8 h-8 text-white\" />,\n      title: t('home.features.support.title'),\n      description: t('home.features.support.description'),\n      color: \"bg-gradient-to-br from-indigo-500 to-indigo-600\",\n      delay: 1000,\n      features: [\n        \"دعم على مدار الساعة\",\n        \"فريق متخصص\",\n        \"استجابة سريعة\"\n      ]\n    }\n  ]\n\n  return (\n    <section className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-flex items-center gap-2 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-4 py-2 rounded-full text-sm font-medium mb-4\">\n            <Sparkles className=\"w-4 h-4\" />\n            ميزاتنا المتقدمة\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text\">\n            {t('home.features.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text\">\n            {t('home.features.subtitle')}\n          </p>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <FeatureCard\n              key={index}\n              icon={feature.icon}\n              title={feature.title}\n              description={feature.description}\n              color={feature.color}\n              delay={feature.delay}\n              features={feature.features}\n            />\n          ))}\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AA0BA,SAAS,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAoB;IACzF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;YACf;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,IAAI,IAAI,OAAO,EAAE;YACf,SAAS,OAAO,CAAC,IAAI,OAAO;QAC9B;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,6BAA6B,EACvC,YAAY,8BAA8B,4BAC1C;QACF,OAAO;YAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;QAAC;kBAEvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC;4BAAI,WAAW,CAAC,4EAA4E,EAAE,MAAM,wDAAwD,CAAC;sCAC3J;;;;;;sCAEH,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAClB;;;;;;;;;;;;8BAGL,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,gIAAA,CAAA,kBAAe;4BAAC,WAAU;sCACxB;;;;;;wBAGF,0BACC,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;mCAFvB;;;;;;;;;;sCAQhB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;gCACX;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;AAEO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE3B,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA;YACE,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;aACD;QACH;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,MAAM,QAAQ,IAAI;4BAClB,OAAO,QAAQ,KAAK;4BACpB,aAAa,QAAQ,WAAW;4BAChC,OAAO,QAAQ,KAAK;4BACpB,OAAO,QAAQ,KAAK;4BACpB,UAAU,QAAQ,QAAQ;2BANrB;;;;;;;;;;;;;;;;;;;;;AAanB", "debugId": null}}, {"offset": {"line": 4403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useProducts.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\n\nexport interface Product {\n  id: string\n  name: string\n  price: string\n  originalPrice?: string\n  image: string\n  rating: number\n  reviews: number\n  isNew?: boolean\n  isFeatured?: boolean\n  category: string\n  description?: string\n  colors?: string[]\n  sizes?: string[]\n}\n\ninterface UseProductsReturn {\n  products: Product[]\n  featuredProducts: Product[]\n  loading: boolean\n  error: string | null\n  refetch: () => void\n}\n\nexport function useProducts(): UseProductsReturn {\n  const [products, setProducts] = useState<Product[]>([])\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const mockProducts: Product[] = [\n    {\n      id: '1',\n      name: 'ثوب التخرج الكلاسيكي الأزرق',\n      price: '450 Dhs',\n      originalPrice: '500 Dhs',\n      image: '/products/gown-1.jpg',\n      rating: 5,\n      reviews: 24,\n      isNew: true,\n      isFeatured: true,\n      category: 'أثواب التخرج',\n      description: 'ثوب تخرج أنيق بتصميم كلاسيكي مصنوع من أجود الأقمشة',\n      colors: ['أزرق', 'أسود', 'أحمر'],\n      sizes: ['S', 'M', 'L', 'XL']\n    },\n    {\n      id: '2',\n      name: 'طقم التخرج الفاخر الأسود',\n      price: '380 Dhs',\n      image: '/products/gown-2.jpg',\n      rating: 4,\n      reviews: 18,\n      isFeatured: true,\n      category: 'أطقم كاملة',\n      description: 'طقم تخرج فاخر يشمل الثوب والقبعة والشرابة',\n      colors: ['أسود', 'أزرق داكن'],\n      sizes: ['S', 'M', 'L', 'XL', 'XXL']\n    },\n    {\n      id: '3',\n      name: 'ثوب التخرج الأنيق الأحمر',\n      price: '420 Dhs',\n      image: '/products/gown-3.jpg',\n      rating: 5,\n      reviews: 31,\n      isNew: true,\n      category: 'أثواب التخرج',\n      description: 'ثوب تخرج بلون أحمر مميز مع تفاصيل ذهبية',\n      colors: ['أحمر', 'بورجوندي'],\n      sizes: ['S', 'M', 'L', 'XL']\n    },\n    {\n      id: '4',\n      name: 'طقم التخرج المميز الذهبي',\n      price: '520 Dhs',\n      image: '/products/gown-4.jpg',\n      rating: 5,\n      reviews: 15,\n      category: 'أطقم فاخرة',\n      description: 'طقم تخرج فاخر بتفاصيل ذهبية للمناسبات الخاصة',\n      colors: ['ذهبي', 'فضي'],\n      sizes: ['M', 'L', 'XL']\n    },\n    {\n      id: '5',\n      name: 'ثوب التخرج العصري الأخضر',\n      price: '390 Dhs',\n      image: '/products/gown-5.jpg',\n      rating: 4,\n      reviews: 22,\n      isNew: true,\n      category: 'أثواب التخرج',\n      description: 'ثوب تخرج بتصميم عصري ولون أخضر مميز',\n      colors: ['أخضر', 'أخضر داكن'],\n      sizes: ['S', 'M', 'L', 'XL']\n    },\n    {\n      id: '6',\n      name: 'طقم التخرج الملكي البنفسجي',\n      price: '480 Dhs',\n      image: '/products/gown-6.jpg',\n      rating: 5,\n      reviews: 19,\n      isFeatured: true,\n      category: 'أطقم فاخرة',\n      description: 'طقم تخرج ملكي بلون بنفسجي مع تطريز فاخر',\n      colors: ['بنفسجي', 'بنفسجي داكن'],\n      sizes: ['S', 'M', 'L', 'XL', 'XXL']\n    }\n  ]\n\n  const fetchProducts = async () => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      // Simulate API call - replace with actual API endpoint\n      await new Promise(resolve => setTimeout(resolve, 1500))\n      \n      // Mock data - replace with actual API response\n      setProducts(mockProducts)\n    } catch (err) {\n      setError('فشل في تحميل المنتجات')\n      console.error('Error fetching products:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const refetch = () => {\n    fetchProducts()\n  }\n\n  useEffect(() => {\n    fetchProducts()\n  }, [])\n\n  const featuredProducts = products.filter(product => product.isFeatured)\n\n  return {\n    products,\n    featuredProducts,\n    loading,\n    error,\n    refetch\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AA4BO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,eAA0B;QAC9B;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,eAAe;YACf,OAAO;YACP,QAAQ;YACR,SAAS;YACT,OAAO;YACP,YAAY;YACZ,UAAU;YACV,aAAa;YACb,QAAQ;gBAAC;gBAAQ;gBAAQ;aAAO;YAChC,OAAO;gBAAC;gBAAK;gBAAK;gBAAK;aAAK;QAC9B;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,UAAU;YACV,aAAa;YACb,QAAQ;gBAAC;gBAAQ;aAAY;YAC7B,OAAO;gBAAC;gBAAK;gBAAK;gBAAK;gBAAM;aAAM;QACrC;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;YACT,OAAO;YACP,UAAU;YACV,aAAa;YACb,QAAQ;gBAAC;gBAAQ;aAAW;YAC5B,OAAO;gBAAC;gBAAK;gBAAK;gBAAK;aAAK;QAC9B;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;YACT,UAAU;YACV,aAAa;YACb,QAAQ;gBAAC;gBAAQ;aAAM;YACvB,OAAO;gBAAC;gBAAK;gBAAK;aAAK;QACzB;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;YACT,OAAO;YACP,UAAU;YACV,aAAa;YACb,QAAQ;gBAAC;gBAAQ;aAAY;YAC7B,OAAO;gBAAC;gBAAK;gBAAK;gBAAK;aAAK;QAC9B;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,UAAU;YACV,aAAa;YACb,QAAQ;gBAAC;gBAAU;aAAc;YACjC,OAAO;gBAAC;gBAAK;gBAAK;gBAAK;gBAAM;aAAM;QACrC;KACD;IAED,MAAM,gBAAgB;QACpB,WAAW;QACX,SAAS;QAET,IAAI;YACF,uDAAuD;YACvD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,+CAA+C;YAC/C,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,UAAU;IAEtE,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 4580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { useProducts, type Product } from \"@/hooks/useProducts\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { \n  ArrowRight, \n  Heart, \n  Star, \n  ShoppingCart,\n  Eye,\n  Sparkles\n} from \"lucide-react\"\n\n\n\ninterface ProductCardProps {\n  product: Product\n  delay: number\n}\n\nfunction ProductCard({ product, delay }: ProductCardProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const [isLiked, setIsLiked] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      className={`transition-all duration-1000 ${\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n      }`}\n      style={{ transitionDelay: `${delay}ms` }}\n    >\n      <Card className=\"group hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 overflow-hidden\">\n        <div className=\"relative\">\n          {/* Product Image */}\n          <div className=\"aspect-[4/5] bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 relative overflow-hidden\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center\">\n              <div className=\"text-6xl\">🎓</div>\n            </div>\n            \n            {/* Badges */}\n            <div className=\"absolute top-3 left-3 flex flex-col gap-2\">\n              {product.isNew && (\n                <Badge className=\"bg-green-500 text-white\">جديد</Badge>\n              )}\n              {product.isFeatured && (\n                <Badge className=\"bg-yellow-500 text-white\">\n                  <Sparkles className=\"w-3 h-3 mr-1\" />\n                  مميز\n                </Badge>\n              )}\n            </div>\n\n            {/* Actions */}\n            <div className=\"absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity\">\n              <Button\n                size=\"sm\"\n                variant=\"secondary\"\n                className=\"w-10 h-10 p-0 rounded-full bg-white/90 hover:bg-white\"\n                onClick={() => setIsLiked(!isLiked)}\n              >\n                <Heart className={`w-4 h-4 ${isLiked ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />\n              </Button>\n              <Button\n                size=\"sm\"\n                variant=\"secondary\"\n                className=\"w-10 h-10 p-0 rounded-full bg-white/90 hover:bg-white\"\n              >\n                <Eye className=\"w-4 h-4 text-gray-600\" />\n              </Button>\n            </div>\n\n            {/* Hover Overlay */}\n            <div className=\"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center\">\n              <Button className=\"bg-white text-gray-900 hover:bg-gray-100\">\n                <ShoppingCart className=\"w-4 h-4 mr-2\" />\n                أضف للسلة\n              </Button>\n            </div>\n          </div>\n\n          {/* Product Info */}\n          <CardContent className=\"p-4\">\n            <div className=\"mb-2\">\n              <Badge variant=\"outline\" className=\"text-xs\">\n                {product.category}\n              </Badge>\n            </div>\n            \n            <h3 className=\"font-semibold text-gray-900 dark:text-white mb-2 arabic-text line-clamp-2\">\n              {product.name}\n            </h3>\n            \n            {/* Rating */}\n            <div className=\"flex items-center gap-1 mb-3\">\n              <div className=\"flex\">\n                {[...Array(5)].map((_, i) => (\n                  <Star\n                    key={i}\n                    className={`w-4 h-4 ${\n                      i < product.rating\n                        ? 'fill-yellow-400 text-yellow-400'\n                        : 'text-gray-300'\n                    }`}\n                  />\n                ))}\n              </div>\n              <span className=\"text-sm text-gray-500\">({product.reviews})</span>\n            </div>\n\n            {/* Price */}\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                {product.price}\n              </span>\n              {product.originalPrice && (\n                <span className=\"text-sm text-gray-500 line-through\">\n                  {product.originalPrice}\n                </span>\n              )}\n            </div>\n          </CardContent>\n        </div>\n      </Card>\n    </div>\n  )\n}\n\nexport function ProductsPreview() {\n  const { t } = useTranslation()\n  const { featuredProducts: products, loading } = useProducts()\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-4\">\n            <ShoppingCart className=\"w-4 h-4\" />\n            منتجاتنا المميزة\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text\">\n            {t('home.products.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text mb-8\">\n            {t('home.products.subtitle')}\n          </p>\n          \n          {/* Category Tabs */}\n          <div className=\"flex flex-wrap justify-center gap-4 mb-8\">\n            <Button variant=\"default\" className=\"arabic-text\">\n              {t('home.products.featured')}\n            </Button>\n            <Button variant=\"outline\" className=\"arabic-text\">\n              {t('home.products.newArrivals')}\n            </Button>\n            <Button variant=\"outline\" className=\"arabic-text\">\n              {t('home.products.popular')}\n            </Button>\n          </div>\n        </div>\n\n        {/* Products Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\">\n          {products.map((product, index) => (\n            <ProductCard\n              key={product.id}\n              product={product}\n              delay={index * 200}\n            />\n          ))}\n        </div>\n\n        {/* View All Button */}\n        <div className=\"text-center\">\n          <Button \n            size=\"lg\" \n            className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 arabic-text group\"\n            asChild\n          >\n            <a href=\"/catalog\" className=\"flex items-center gap-2\">\n              {t('home.products.viewAll')}\n              <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n            </a>\n          </Button>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAwBA,SAAS,YAAY,EAAE,OAAO,EAAE,KAAK,EAAoB;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;YACf;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,IAAI,IAAI,OAAO,EAAE;YACf,SAAS,OAAO,CAAC,IAAI,OAAO;QAC9B;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,6BAA6B,EACvC,YAAY,8BAA8B,4BAC1C;QACF,OAAO;YAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;QAAC;kBAEvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAW;;;;;;;;;;;0CAI5B,8OAAC;gCAAI,WAAU;;oCACZ,QAAQ,KAAK,kBACZ,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAA0B;;;;;;oCAE5C,QAAQ,UAAU,kBACjB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;;0DACf,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAO3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,WAAW,CAAC;kDAE3B,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,8BAA8B,iBAAiB;;;;;;;;;;;kDAExF,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;kDAEV,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAO/C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAChC,QAAQ,QAAQ;;;;;;;;;;;0CAIrB,8OAAC;gCAAG,WAAU;0CACX,QAAQ,IAAI;;;;;;0CAIf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ;+CAAI,MAAM;yCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;gDAEH,WAAW,CAAC,QAAQ,EAClB,IAAI,QAAQ,MAAM,GACd,oCACA,iBACJ;+CALG;;;;;;;;;;kDASX,8OAAC;wCAAK,WAAU;;4CAAwB;4CAAE,QAAQ,OAAO;4CAAC;;;;;;;;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,QAAQ,KAAK;;;;;;oCAEf,QAAQ,aAAa,kBACpB,8OAAC;wCAAK,WAAU;kDACb,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;AAEO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EAAE,kBAAkB,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE1D,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGtC,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAIL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;8CACjC,EAAE;;;;;;8CAEL,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;8CACjC,EAAE;;;;;;8CAEL,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;8CACjC,EAAE;;;;;;;;;;;;;;;;;;8BAMT,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,SAAS;4BACT,OAAO,QAAQ;2BAFV,QAAQ,EAAE;;;;;;;;;;8BAQrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,WAAU;wBACV,OAAO;kCAEP,cAAA,8OAAC;4BAAE,MAAK;4BAAW,WAAU;;gCAC1B,EAAE;8CACH,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 5022, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTestimonials.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\n\nexport interface Testimonial {\n  id: string\n  name: string\n  role: string\n  university: string\n  content: string\n  rating: number\n  avatar: string\n  date: string\n  verified?: boolean\n}\n\ninterface UseTestimonialsReturn {\n  testimonials: Testimonial[]\n  loading: boolean\n  error: string | null\n  refetch: () => void\n}\n\nexport function useTestimonials(): UseTestimonialsReturn {\n  const [testimonials, setTestimonials] = useState<Testimonial[]>([])\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const mockTestimonials: Testimonial[] = [\n    {\n      id: '1',\n      name: 'أمينة الحسني',\n      role: 'طالبة ماجستير',\n      university: 'جامعة محمد الخامس',\n      content: 'تجربة رائعة! الثوب كان مثالياً وجودة القماش عالية جداً. الخدمة كانت احترافية والتسليم في الوقت المحدد.',\n      rating: 5,\n      avatar: '/avatars/amina.jpg',\n      date: 'منذ أسبوعين',\n      verified: true\n    },\n    {\n      id: '2',\n      name: 'يوسف بنعلي',\n      role: 'طالب دكتوراه',\n      university: 'جامعة الحسن الثاني',\n      content: 'المنصة سهلة الاستخدام والتخصيص كان ممتازاً. حصلت على ثوب التخرج المثالي بفضل المساعد الذكي.',\n      rating: 5,\n      avatar: '/avatars/youssef.jpg',\n      date: 'منذ 3 أسابيع',\n      verified: true\n    },\n    {\n      id: '3',\n      name: 'فاطمة الزهراء',\n      role: 'طالبة بكالوريوس',\n      university: 'جامعة القاضي عياض',\n      content: 'أعجبني التنوع في التصاميم والألوان. الفريق كان متعاوناً جداً وساعدني في اختيار الأفضل.',\n      rating: 5,\n      avatar: '/avatars/fatima.jpg',\n      date: 'منذ شهر',\n      verified: true\n    },\n    {\n      id: '4',\n      name: 'عبد الرحمن التازي',\n      role: 'طالب هندسة',\n      university: 'المدرسة المحمدية للمهندسين',\n      content: 'جودة عالية وأسعار معقولة. التتبع كان دقيقاً والتسليم سريع. أنصح بها بشدة!',\n      rating: 5,\n      avatar: '/avatars/abderrahman.jpg',\n      date: 'منذ شهر ونصف',\n      verified: true\n    },\n    {\n      id: '5',\n      name: 'خديجة المرابط',\n      role: 'طالبة طب',\n      university: 'جامعة سيدي محمد بن عبد الله',\n      content: 'تجربة استثنائية من البداية للنهاية. الثوب كان أنيقاً ومريحاً، والخدمة كانت ممتازة.',\n      rating: 5,\n      avatar: '/avatars/khadija.jpg',\n      date: 'منذ شهرين',\n      verified: true\n    },\n    {\n      id: '6',\n      name: 'محمد الإدريسي',\n      role: 'طالب إدارة أعمال',\n      university: 'جامعة ابن طفيل',\n      content: 'المنصة احترافية والذكاء الاصطناعي ساعدني كثيراً في الاختيار. النتيجة كانت فوق التوقعات.',\n      rating: 5,\n      avatar: '/avatars/mohammed.jpg',\n      date: 'منذ شهرين ونصف',\n      verified: true\n    },\n    {\n      id: '7',\n      name: 'سارة العلوي',\n      role: 'طالبة صيدلة',\n      university: 'جامعة الرباط',\n      content: 'خدمة عملاء ممتازة وتصاميم رائعة. أحببت إمكانية التخصيص والمعاينة المسبقة.',\n      rating: 4,\n      avatar: '/avatars/sara.jpg',\n      date: 'منذ 3 أشهر',\n      verified: true\n    },\n    {\n      id: '8',\n      name: 'أحمد الفاسي',\n      role: 'طالب قانون',\n      university: 'جامعة فاس',\n      content: 'تجربة مميزة وسعر مناسب. الثوب وصل في الوقت المحدد وكان بالجودة المطلوبة.',\n      rating: 5,\n      avatar: '/avatars/ahmed.jpg',\n      date: 'منذ 3 أشهر ونصف',\n      verified: true\n    }\n  ]\n\n  const fetchTestimonials = async () => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      // Simulate API call - replace with actual API endpoint\n      await new Promise(resolve => setTimeout(resolve, 1200))\n      \n      // Mock data - replace with actual API response\n      setTestimonials(mockTestimonials)\n    } catch (err) {\n      setError('فشل في تحميل آراء العملاء')\n      console.error('Error fetching testimonials:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const refetch = () => {\n    fetchTestimonials()\n  }\n\n  useEffect(() => {\n    fetchTestimonials()\n  }, [])\n\n  return {\n    testimonials,\n    loading,\n    error,\n    refetch\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAuBO,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,mBAAkC;QACtC;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;KACD;IAED,MAAM,oBAAoB;QACxB,WAAW;QACX,SAAS;QAET,IAAI;YACF,uDAAuD;YACvD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,+CAA+C;YAC/C,gBAAgB;QAClB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 5156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { useTestimonials, type Testimonial } from \"@/hooks/useTestimonials\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { \n  Star, \n  Quote,\n  ArrowLeft,\n  ArrowRight,\n  MessageSquare\n} from \"lucide-react\"\n\n\n\ninterface TestimonialCardProps {\n  testimonial: Testimonial\n  delay: number\n}\n\nfunction TestimonialCard({ testimonial, delay }: TestimonialCardProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      className={`transition-all duration-1000 ${\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n      }`}\n      style={{ transitionDelay: `${delay}ms` }}\n    >\n      <Card className=\"h-full hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 relative overflow-hidden\">\n        {/* Quote Icon */}\n        <div className=\"absolute top-4 right-4 text-blue-100 dark:text-blue-900\">\n          <Quote className=\"w-8 h-8\" />\n        </div>\n\n        <CardContent className=\"p-6\">\n          {/* Rating */}\n          <div className=\"flex items-center gap-1 mb-4\">\n            {[...Array(5)].map((_, i) => (\n              <Star\n                key={i}\n                className={`w-4 h-4 ${\n                  i < testimonial.rating\n                    ? 'fill-yellow-400 text-yellow-400'\n                    : 'text-gray-300'\n                }`}\n              />\n            ))}\n          </div>\n\n          {/* Content */}\n          <p className=\"text-gray-700 dark:text-gray-300 arabic-text leading-relaxed mb-6 text-lg\">\n            \"{testimonial.content}\"\n          </p>\n\n          {/* Author Info */}\n          <div className=\"flex items-center gap-4\">\n            <Avatar className=\"w-12 h-12\">\n              <AvatarImage src={testimonial.avatar} alt={testimonial.name} />\n              <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-500 text-white\">\n                {testimonial.name.split(' ').map(n => n[0]).join('')}\n              </AvatarFallback>\n            </Avatar>\n            <div>\n              <h4 className=\"font-semibold text-gray-900 dark:text-white arabic-text\">\n                {testimonial.name}\n              </h4>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400 arabic-text\">\n                {testimonial.role}\n              </p>\n              <p className=\"text-xs text-blue-600 dark:text-blue-400 arabic-text\">\n                {testimonial.university}\n              </p>\n            </div>\n          </div>\n\n          {/* Date */}\n          <div className=\"mt-4 text-xs text-gray-400\">\n            {testimonial.date}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nexport function TestimonialsSection() {\n  const { t } = useTranslation()\n  const { testimonials, loading } = useTestimonials()\n  const [currentIndex, setCurrentIndex] = useState(0)\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prev) => (prev + 1) % Math.ceil(testimonials.length / 3))\n  }\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prev) => (prev - 1 + Math.ceil(testimonials.length / 3)) % Math.ceil(testimonials.length / 3))\n  }\n\n  const visibleTestimonials = testimonials.slice(currentIndex * 3, (currentIndex + 1) * 3)\n\n  return (\n    <section className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-flex items-center gap-2 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-4 py-2 rounded-full text-sm font-medium mb-4\">\n            <MessageSquare className=\"w-4 h-4\" />\n            آراء العملاء\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text\">\n            {t('home.testimonials.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text\">\n            {t('home.testimonials.subtitle')}\n          </p>\n        </div>\n\n        {/* Testimonials Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n          {visibleTestimonials.map((testimonial, index) => (\n            <TestimonialCard\n              key={testimonial.id}\n              testimonial={testimonial}\n              delay={index * 200}\n            />\n          ))}\n        </div>\n\n        {/* Navigation */}\n        <div className=\"flex items-center justify-center gap-4\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={prevTestimonial}\n            className=\"w-10 h-10 p-0 rounded-full\"\n          >\n            <ArrowRight className=\"w-4 h-4\" />\n          </Button>\n          \n          <div className=\"flex gap-2\">\n            {Array.from({ length: Math.ceil(testimonials.length / 3) }).map((_, index) => (\n              <button\n                key={index}\n                onClick={() => setCurrentIndex(index)}\n                className={`w-2 h-2 rounded-full transition-colors ${\n                  index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'\n                }`}\n              />\n            ))}\n          </div>\n          \n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={nextTestimonial}\n            className=\"w-10 h-10 p-0 rounded-full\"\n          >\n            <ArrowLeft className=\"w-4 h-4\" />\n          </Button>\n        </div>\n\n        {/* View All Button */}\n        <div className=\"text-center mt-8\">\n          <Button variant=\"ghost\" className=\"text-blue-600 dark:text-blue-400 arabic-text\">\n            {t('home.testimonials.viewAll')}\n          </Button>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAuBA,SAAS,gBAAgB,EAAE,WAAW,EAAE,KAAK,EAAwB;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;YACf;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,IAAI,IAAI,OAAO,EAAE;YACf,SAAS,OAAO,CAAC,IAAI,OAAO;QAC9B;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,6BAA6B,EACvC,YAAY,8BAA8B,4BAC1C;QACF,OAAO;YAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;QAAC;kBAEvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BAEd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;8BAGnB,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;oCAEH,WAAW,CAAC,QAAQ,EAClB,IAAI,YAAY,MAAM,GAClB,oCACA,iBACJ;mCALG;;;;;;;;;;sCAWX,8OAAC;4BAAE,WAAU;;gCAA4E;gCACrF,YAAY,OAAO;gCAAC;;;;;;;sCAIxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kIAAA,CAAA,cAAW;4CAAC,KAAK,YAAY,MAAM;4CAAE,KAAK,YAAY,IAAI;;;;;;sDAC3D,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;8CAGrD,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,YAAY,IAAI;;;;;;sDAEnB,8OAAC;4CAAE,WAAU;sDACV,YAAY,IAAI;;;;;;sDAEnB,8OAAC;4CAAE,WAAU;sDACV,YAAY,UAAU;;;;;;;;;;;;;;;;;;sCAM7B,8OAAC;4BAAI,WAAU;sCACZ,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAM7B;AAEO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG;IACzE;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG,EAAE,IAAI,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG;IAC9G;IAEA,MAAM,sBAAsB,aAAa,KAAK,CAAC,eAAe,GAAG,CAAC,eAAe,CAAC,IAAI;IAEtF,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGvC,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;8BACZ,oBAAoB,GAAG,CAAC,CAAC,aAAa,sBACrC,8OAAC;4BAEC,aAAa;4BACb,OAAO,QAAQ;2BAFV,YAAY,EAAE;;;;;;;;;;8BAQzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;sCAGxB,8OAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG;4BAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClE,8OAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,uCAAuC,EACjD,UAAU,eAAe,gBAAgB,eACzC;mCAJG;;;;;;;;;;sCASX,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKzB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,WAAU;kCAC/B,EAAE;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 5561, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { \n  Brain, \n  MessageSquare, \n  Eye, \n  Lightbulb,\n  Sparkles,\n  Cpu,\n  Zap,\n  ArrowRight,\n  Bot,\n  Palette,\n  Camera,\n  Wand2\n} from \"lucide-react\"\n\ninterface AIFeatureProps {\n  icon: React.ReactNode\n  title: string\n  description: string\n  color: string\n  delay: number\n  isActive?: boolean\n}\n\nfunction AIFeatureCard({ icon, title, description, color, delay, isActive }: AIFeatureProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      className={`transition-all duration-1000 ${\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n      }`}\n      style={{ transitionDelay: `${delay}ms` }}\n    >\n      <Card className={`h-full hover:shadow-xl transition-all duration-300 border-0 bg-white dark:bg-gray-800 group hover:-translate-y-2 ${\n        isActive ? 'ring-2 ring-blue-500 shadow-lg' : ''\n      }`}>\n        <CardHeader className=\"text-center pb-4\">\n          <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 mx-auto ${color} group-hover:scale-110 transition-transform duration-300 relative`}>\n            {icon}\n            {isActive && (\n              <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white\">\n                <div className=\"w-full h-full bg-green-500 rounded-full animate-ping\"></div>\n              </div>\n            )}\n          </div>\n          <CardTitle className=\"text-xl font-bold text-gray-900 dark:text-white arabic-text mb-2\">\n            {title}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"text-center\">\n          <CardDescription className=\"text-gray-600 dark:text-gray-300 arabic-text leading-relaxed mb-4\">\n            {description}\n          </CardDescription>\n          \n          <Button \n            variant=\"ghost\" \n            size=\"sm\" \n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 group/btn\"\n          >\n            جرب الآن\n            <ArrowRight className=\"w-4 h-4 mr-2 group-hover/btn:translate-x-1 transition-transform\" />\n          </Button>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nfunction AIModelCard({ name, provider, status, description, delay }: {\n  name: string\n  provider: string\n  status: 'active' | 'inactive'\n  description: string\n  delay: number\n}) {\n  const [isVisible, setIsVisible] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      className={`transition-all duration-1000 ${\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n      }`}\n      style={{ transitionDelay: `${delay}ms` }}\n    >\n      <Card className=\"hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700\">\n        <CardContent className=\"p-4\">\n          <div className=\"flex items-center justify-between mb-2\">\n            <div className=\"flex items-center gap-2\">\n              <Cpu className=\"w-5 h-5 text-blue-600\" />\n              <span className=\"font-semibold text-gray-900 dark:text-white\">{name}</span>\n            </div>\n            <Badge \n              variant={status === 'active' ? 'default' : 'secondary'}\n              className={status === 'active' ? 'bg-green-500' : ''}\n            >\n              {status === 'active' ? 'نشط' : 'غير نشط'}\n            </Badge>\n          </div>\n          <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-2\">{provider}</p>\n          <p className=\"text-sm text-gray-600 dark:text-gray-300 arabic-text\">{description}</p>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nexport function AIFeaturesSection() {\n  const { t } = useTranslation()\n\n  const aiFeatures = [\n    {\n      icon: <Bot className=\"w-8 h-8 text-white\" />,\n      title: t('home.ai.features.assistant.title'),\n      description: t('home.ai.features.assistant.description'),\n      color: \"bg-gradient-to-br from-blue-500 to-blue-600\",\n      delay: 0,\n      isActive: true\n    },\n    {\n      icon: <Eye className=\"w-8 h-8 text-white\" />,\n      title: t('home.ai.features.visualization.title'),\n      description: t('home.ai.features.visualization.description'),\n      color: \"bg-gradient-to-br from-purple-500 to-purple-600\",\n      delay: 200,\n      isActive: true\n    },\n    {\n      icon: <Lightbulb className=\"w-8 h-8 text-white\" />,\n      title: t('home.ai.features.recommendations.title'),\n      description: t('home.ai.features.recommendations.description'),\n      color: \"bg-gradient-to-br from-yellow-500 to-yellow-600\",\n      delay: 400,\n      isActive: true\n    },\n    {\n      icon: <MessageSquare className=\"w-8 h-8 text-white\" />,\n      title: t('home.ai.features.chat.title'),\n      description: t('home.ai.features.chat.description'),\n      color: \"bg-gradient-to-br from-green-500 to-green-600\",\n      delay: 600,\n      isActive: true\n    }\n  ]\n\n  const aiModels = [\n    {\n      name: 'GPT-4',\n      provider: 'OpenAI',\n      status: 'active' as const,\n      description: 'نموذج متقدم للمحادثة والتوصيات الذكية',\n      delay: 0\n    },\n    {\n      name: 'Claude 3',\n      provider: 'Anthropic',\n      status: 'active' as const,\n      description: 'مساعد ذكي للتحليل والاقتراحات المخصصة',\n      delay: 200\n    },\n    {\n      name: 'Gemini Pro',\n      provider: 'Google',\n      status: 'active' as const,\n      description: 'نموذج متعدد الوسائط للمعاينة والتصميم',\n      delay: 400\n    },\n    {\n      name: 'Mistral AI',\n      provider: 'Mistral',\n      status: 'inactive' as const,\n      description: 'نموذج سريع للاستجابات الفورية',\n      delay: 600\n    }\n  ]\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium mb-4\">\n            <Brain className=\"w-4 h-4\" />\n            الذكاء الاصطناعي\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 arabic-text\">\n            {t('home.ai.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto arabic-text\">\n            {t('home.ai.subtitle')}\n          </p>\n        </div>\n\n        {/* AI Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n          {aiFeatures.map((feature, index) => (\n            <AIFeatureCard\n              key={index}\n              icon={feature.icon}\n              title={feature.title}\n              description={feature.description}\n              color={feature.color}\n              delay={feature.delay}\n              isActive={feature.isActive}\n            />\n          ))}\n        </div>\n\n        {/* AI Models Section */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg\">\n          <div className=\"text-center mb-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text\">\n              نماذج الذكاء الاصطناعي المتاحة\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-300 arabic-text\">\n              نستخدم أحدث نماذج الذكاء الاصطناعي لتقديم أفضل تجربة\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            {aiModels.map((model, index) => (\n              <AIModelCard\n                key={index}\n                name={model.name}\n                provider={model.provider}\n                status={model.status}\n                description={model.description}\n                delay={index * 100}\n              />\n            ))}\n          </div>\n\n          {/* AI Demo Section */}\n          <div className=\"text-center\">\n            <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 mb-6\">\n              <div className=\"flex items-center justify-center gap-2 mb-4\">\n                <Sparkles className=\"w-6 h-6 text-blue-600\" />\n                <span className=\"text-lg font-semibold text-gray-900 dark:text-white arabic-text\">\n                  جرب المساعد الذكي الآن\n                </span>\n              </div>\n              <p className=\"text-gray-600 dark:text-gray-300 arabic-text mb-4\">\n                اطلب من مساعدنا الذكي مساعدتك في اختيار ثوب التخرج المثالي\n              </p>\n              <div className=\"flex flex-wrap justify-center gap-4\">\n                <Button className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white\">\n                  <Wand2 className=\"w-4 h-4 mr-2\" />\n                  ابدأ المحادثة\n                </Button>\n                <Button variant=\"outline\">\n                  <Camera className=\"w-4 h-4 mr-2\" />\n                  معاينة ثلاثية الأبعاد\n                </Button>\n                <Button variant=\"outline\">\n                  <Palette className=\"w-4 h-4 mr-2\" />\n                  تخصيص ذكي\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AA+BA,SAAS,cAAc,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAkB;IACzF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;YACf;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,IAAI,IAAI,OAAO,EAAE;YACf,SAAS,OAAO,CAAC,IAAI,OAAO;QAC9B;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,6BAA6B,EACvC,YAAY,8BAA8B,4BAC1C;QACF,OAAO;YAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;QAAC;kBAEvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW,CAAC,iHAAiH,EACjI,WAAW,mCAAmC,IAC9C;;8BACA,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC;4BAAI,WAAW,CAAC,4EAA4E,EAAE,MAAM,iEAAiE,CAAC;;gCACpK;gCACA,0BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAIrB,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAClB;;;;;;;;;;;;8BAGL,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC,gIAAA,CAAA,kBAAe;4BAAC,WAAU;sCACxB;;;;;;sCAGH,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;gCACX;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;AAEA,SAAS,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAMhE;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;YACf;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,IAAI,IAAI,OAAO,EAAE;YACf,SAAS,OAAO,CAAC,IAAI,OAAO;QAC9B;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,6BAA6B,EACvC,YAAY,8BAA8B,4BAC1C;QACF,OAAO;YAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;QAAC;kBAEvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAEjE,8OAAC,iIAAA,CAAA,QAAK;gCACJ,SAAS,WAAW,WAAW,YAAY;gCAC3C,WAAW,WAAW,WAAW,iBAAiB;0CAEjD,WAAW,WAAW,QAAQ;;;;;;;;;;;;kCAGnC,8OAAC;wBAAE,WAAU;kCAAiD;;;;;;kCAC9D,8OAAC;wBAAE,WAAU;kCAAwD;;;;;;;;;;;;;;;;;;;;;;AAK/E;AAEO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE3B,MAAM,aAAa;QACjB;YACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;QACZ;QACA;YACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;QACZ;QACA;YACE,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;QACZ;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,OAAO;YACP,UAAU;QACZ;KACD;IAED,MAAM,WAAW;QACf;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,SAAS,sBACxB,8OAAC;4BAEC,MAAM,QAAQ,IAAI;4BAClB,OAAO,QAAQ,KAAK;4BACpB,aAAa,QAAQ,WAAW;4BAChC,OAAO,QAAQ,KAAK;4BACpB,OAAO,QAAQ,KAAK;4BACpB,UAAU,QAAQ,QAAQ;2BANrB;;;;;;;;;;8BAYX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAK9D,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,8OAAC;oCAEC,MAAM,MAAM,IAAI;oCAChB,UAAU,MAAM,QAAQ;oCACxB,QAAQ,MAAM,MAAM;oCACpB,aAAa,MAAM,WAAW;oCAC9B,OAAO,QAAQ;mCALV;;;;;;;;;;sCAWX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAAkE;;;;;;;;;;;;kDAIpF,8OAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,+MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;;kEACd,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;;kEACd,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtD", "debugId": null}}, {"offset": {"line": 6139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/home/<USER>"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Button } from \"@/components/ui/button\"\nimport { \n  ArrowRight, \n  Sparkles, \n  MessageSquare,\n  GraduationCap,\n  Star,\n  Users\n} from \"lucide-react\"\n\nexport function CTASection() {\n  const { t } = useTranslation()\n  const [isVisible, setIsVisible] = useState(false)\n  const ref = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true)\n        }\n      },\n      { threshold: 0.1 }\n    )\n\n    if (ref.current) {\n      observer.observe(ref.current)\n    }\n\n    return () => observer.disconnect()\n  }, [])\n\n  return (\n    <section \n      ref={ref}\n      className=\"py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden\"\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-20 h-20 border border-white rounded-full animate-pulse\" />\n        <div className=\"absolute top-32 right-20 w-16 h-16 border border-white rounded-full animate-bounce\" />\n        <div className=\"absolute bottom-20 left-32 w-24 h-24 border border-white rounded-full animate-pulse\" />\n        <div className=\"absolute bottom-32 right-10 w-12 h-12 border border-white rounded-full animate-bounce\" />\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          {/* Main Content */}\n          <div className={`transition-all duration-1000 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            {/* Icon */}\n            <div className=\"inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-6\">\n              <GraduationCap className=\"w-10 h-10 text-white\" />\n            </div>\n\n            {/* Title */}\n            <h2 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 arabic-text leading-tight\">\n              {t('home.cta.title')}\n            </h2>\n\n            {/* Subtitle */}\n            <p className=\"text-xl md:text-2xl text-blue-100 mb-4 arabic-text\">\n              {t('home.cta.subtitle')}\n            </p>\n\n            {/* Description */}\n            <p className=\"text-lg text-blue-200 mb-12 max-w-2xl mx-auto arabic-text leading-relaxed\">\n              {t('home.cta.description')}\n            </p>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n              <div className=\"text-center\">\n                <div className=\"flex items-center justify-center gap-2 mb-2\">\n                  <Users className=\"w-6 h-6 text-blue-200\" />\n                  <span className=\"text-3xl font-bold text-white\">1,200+</span>\n                </div>\n                <p className=\"text-blue-200 arabic-text\">طالب راضٍ</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"flex items-center justify-center gap-2 mb-2\">\n                  <Star className=\"w-6 h-6 text-yellow-300\" />\n                  <span className=\"text-3xl font-bold text-white\">4.9</span>\n                </div>\n                <p className=\"text-blue-200 arabic-text\">تقييم العملاء</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"flex items-center justify-center gap-2 mb-2\">\n                  <GraduationCap className=\"w-6 h-6 text-blue-200\" />\n                  <span className=\"text-3xl font-bold text-white\">50+</span>\n                </div>\n                <p className=\"text-blue-200 arabic-text\">مدرسة وجامعة</p>\n              </div>\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <Button \n                size=\"lg\" \n                className=\"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg font-semibold arabic-text shadow-lg hover:shadow-xl transition-all duration-300 group\"\n                asChild\n              >\n                <a href=\"/customize\" className=\"flex items-center gap-2\">\n                  <Sparkles className=\"w-5 h-5\" />\n                  {t('home.cta.button')}\n                  <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n                </a>\n              </Button>\n              \n              <Button \n                variant=\"outline\" \n                size=\"lg\" \n                className=\"border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold arabic-text transition-all duration-300\"\n                asChild\n              >\n                <a href=\"/contact\" className=\"flex items-center gap-2\">\n                  <MessageSquare className=\"w-5 h-5\" />\n                  {t('home.cta.contact')}\n                </a>\n              </Button>\n            </div>\n          </div>\n\n          {/* Additional Info */}\n          <div className={`mt-16 transition-all duration-1000 delay-500 ${\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n          }`}>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\">\n                <div>\n                  <div className=\"text-2xl font-bold text-white mb-2\">✅</div>\n                  <p className=\"text-blue-100 arabic-text\">ضمان الجودة</p>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-white mb-2\">🚚</div>\n                  <p className=\"text-blue-100 arabic-text\">توصيل مجاني</p>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-white mb-2\">🔄</div>\n                  <p className=\"text-blue-100 arabic-text\">إرجاع مجاني</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Decorative Elements */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-50 dark:from-gray-900 to-transparent\" />\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;YACf;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,IAAI,IAAI,OAAO,EAAE;YACf,SAAS,OAAO,CAAC,IAAI,OAAO;QAC9B;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAW,CAAC,6BAA6B,EAC5C,YAAY,8BAA8B,4BAC1C;;8CAEA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAI3B,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAIL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;8DAElD,8OAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;sDAE3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;8DAElD,8OAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;sDAE3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;sEACzB,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;8DAElD,8OAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,OAAO;sDAEP,cAAA,8OAAC;gDAAE,MAAK;gDAAa,WAAU;;kEAC7B,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDACnB,EAAE;kEACH,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAI1B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,OAAO;sDAEP,cAAA,8OAAC;gDAAE,MAAK;gDAAW,WAAU;;kEAC3B,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDACxB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;sCAOX,8OAAC;4BAAI,WAAW,CAAC,6CAA6C,EAC5D,YAAY,8BAA8B,4BAC1C;sCACA,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;sDAE3C,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;sDAE3C,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrD,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 6597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { PageLayout } from \"@/components/layouts/PageLayout\"\nimport { HeroSection } from \"@/components/home/<USER>\"\nimport { StatsSection } from \"@/components/home/<USER>\"\nimport { FeaturesShowcase } from \"@/components/home/<USER>\"\nimport { ProductsPreview } from \"@/components/home/<USER>\"\nimport { TestimonialsSection } from \"@/components/home/<USER>\"\nimport { AIFeaturesSection } from \"@/components/home/<USER>\"\nimport { CTASection } from \"@/components/home/<USER>\"\n\nexport default function Home() {\n  return (\n    <PageLayout containerClassName=\"\" showFooter={true}>\n      {/* Hero Section - القسم الرئيسي */}\n      <HeroSection />\n\n      {/* Stats Section - قسم الإحصائيات */}\n      <StatsSection />\n\n      {/* Features Showcase - عرض الميزات */}\n      <FeaturesShowcase />\n\n      {/* Products Preview - معاينة المنتجات */}\n      <ProductsPreview />\n\n      {/* AI Features Section - قسم الذكاء الاصطناعي */}\n      <AIFeaturesSection />\n\n      {/* Testimonials Section - قسم آراء العملاء */}\n      <TestimonialsSection />\n\n      {/* Call to Action Section - قسم الدعوة للعمل */}\n      <CTASection />\n    </PageLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,qBACE,8OAAC,2IAAA,CAAA,aAAU;QAAC,oBAAmB;QAAG,YAAY;;0BAE5C,8OAAC,yIAAA,CAAA,cAAW;;;;;0BAGZ,8OAAC,0IAAA,CAAA,eAAY;;;;;0BAGb,8OAAC,8IAAA,CAAA,mBAAgB;;;;;0BAGjB,8OAAC,6IAAA,CAAA,kBAAe;;;;;0BAGhB,8OAAC,+IAAA,CAAA,oBAAiB;;;;;0BAGlB,8OAAC,iJAAA,CAAA,sBAAmB;;;;;0BAGpB,8OAAC,wIAAA,CAAA,aAAU;;;;;;;;;;;AAGjB", "debugId": null}}]}